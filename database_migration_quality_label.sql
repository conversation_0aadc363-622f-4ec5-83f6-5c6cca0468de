-- Add quality_label column to content table
-- This column will store the homepage display quality label separate from streaming quality info

ALTER TABLE content ADD COLUMN quality_label VARCHAR(100) DEFAULT NULL AFTER quality;

-- Add index for better performance when filtering by quality label
CREATE INDEX idx_content_quality_label ON content(quality_label);

-- Update existing content with quality labels based on existing quality data (optional)
-- You can run these updates manually if you want to populate existing content

-- Example updates (uncomment and modify as needed):
-- UPDATE content SET quality_label = 'HD' WHERE quality LIKE '%HD%' AND quality_label IS NULL;
-- UPDATE content SET quality_label = '4K' WHERE quality LIKE '%4K%' AND quality_label IS NULL;
-- UPDATE content SET quality_label = 'BluRay' WHERE quality LIKE '%BluRay%' AND quality_label IS NULL;
-- UPDATE content SET quality_label = 'WEB-DL' WHERE quality LIKE '%WEB%' AND quality_label IS NULL;