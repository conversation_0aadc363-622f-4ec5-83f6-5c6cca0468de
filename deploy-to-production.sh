#!/bin/bash

# StreamDB Production Deployment Script
# Deploy Web Series Management System to Production Server

echo "🚀 Starting StreamDB Web Series Management System Deployment"
echo "============================================================"

# Production server details
PROD_SERVER="***********"
PROD_PATH="/var/www/streamdb_root/data/www/streamdb.online"
LOCAL_PATH="G:/My Websites/Catalogue-Website/the-stream-db/Streaming_DB"

echo "📋 Deployment Summary:"
echo "- Local Path: $LOCAL_PATH"
echo "- Production Server: $PROD_SERVER"
echo "- Production Path: $PROD_PATH"
echo ""

# Files to deploy
FILES_TO_DEPLOY=(
    "src/components/admin/AddTitleForm.tsx"
    "src/components/admin/WebSeriesManager.tsx"
    "src/types/admin.ts"
    "server/routes/episodes.js"
)

echo "📁 Files to deploy:"
for file in "${FILES_TO_DEPLOY[@]}"; do
    echo "  - $file"
done
echo ""

# Step 1: Backup current production files
echo "💾 Step 1: Creating backup of production files..."
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && mkdir -p backups/$(date +%Y%m%d_%H%M%S)'"
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && cp -r src/components/admin backups/$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true'"
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && cp -r src/types backups/$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true'"
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && cp -r server/routes backups/$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true'"
echo ""

# Step 2: Deploy files
echo "📤 Step 2: Deploying files to production server..."
for file in "${FILES_TO_DEPLOY[@]}"; do
    echo "scp \"$LOCAL_PATH/$file\" root@$PROD_SERVER:\"$PROD_PATH/$file\""
done
echo ""

# Step 3: Restart backend services
echo "🔄 Step 3: Restarting backend services..."
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && pm2 restart index'"
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && pm2 restart webhook-server'"
echo ""

# Step 4: Rebuild frontend
echo "🏗️ Step 4: Rebuilding frontend..."
echo "ssh root@$PROD_SERVER 'cd $PROD_PATH && npm run build'"
echo ""

# Step 5: Verify deployment
echo "✅ Step 5: Verification steps..."
echo "1. Check PM2 status: ssh root@$PROD_SERVER 'pm2 status'"
echo "2. Check logs: ssh root@$PROD_SERVER 'pm2 logs index --lines 20'"
echo "3. Test admin panel: https://streamdb.online/admin"
echo "4. Test web series management functionality"
echo ""

echo "🎯 Manual Deployment Commands:"
echo "=============================="
echo ""
echo "# 1. Connect to production server"
echo "ssh root@$PROD_SERVER"
echo ""
echo "# 2. Navigate to project directory"
echo "cd $PROD_PATH"
echo ""
echo "# 3. Create backup"
echo "mkdir -p backups/\$(date +%Y%m%d_%H%M%S)"
echo "cp -r src/components/admin src/types server/routes backups/\$(date +%Y%m%d_%H%M%S)/"
echo ""
echo "# 4. Copy files from local (run from local machine):"
for file in "${FILES_TO_DEPLOY[@]}"; do
    echo "scp \"$LOCAL_PATH/$file\" root@$PROD_SERVER:\"$PROD_PATH/$file\""
done
echo ""
echo "# 5. Restart services (on production server)"
echo "pm2 restart index"
echo "pm2 restart webhook-server"
echo ""
echo "# 6. Rebuild frontend (on production server)"
echo "npm run build"
echo ""
echo "# 7. Check status"
echo "pm2 status"
echo "pm2 logs index --lines 20"
echo ""

echo "🔍 Post-Deployment Testing:"
echo "=========================="
echo "1. Visit: https://streamdb.online/admin"
echo "2. Test 'Add New Content' → Select 'Web Series' → Check 'Seasons & Episodes' section"
echo "3. Test 'All Web-Series' tab → Verify table layout and CRUD operations"
echo "4. Test mobile responsiveness on different screen sizes"
echo "5. Verify database operations work correctly"
echo ""

echo "📱 Mobile Testing Breakpoints:"
echo "- 320px (Mobile Portrait)"
echo "- 768px (Tablet)"
echo "- 1024px (Desktop)"
echo ""

echo "🚨 Rollback Instructions (if needed):"
echo "====================================="
echo "ssh root@$PROD_SERVER"
echo "cd $PROD_PATH"
echo "# Find latest backup"
echo "ls -la backups/"
echo "# Restore from backup"
echo "cp -r backups/[BACKUP_TIMESTAMP]/* ."
echo "pm2 restart index"
echo "npm run build"
echo ""

echo "✨ Deployment script ready!"
echo "Run the manual commands above to deploy to production."
