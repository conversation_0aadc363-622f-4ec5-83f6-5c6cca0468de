-- Database Migration: Add quality_label column to content table
-- Run this SQL command in your MySQL database
-- Database: stream_db (from .env configuration)

USE stream_db;

-- Add the quality_label column after the quality column
ALTER TABLE content ADD COLUMN quality_label VARCHAR(100) DEFAULT NULL AFTER quality;

-- Add index for better performance when filtering by quality label
CREATE INDEX idx_content_quality_label ON content(quality_label);

-- Verify the column was added successfully
DESCRIBE content;

-- Optional: Update existing content with quality labels based on existing quality data
-- Uncomment and run these if you want to populate existing content automatically:

-- UPDATE stream_db.content SET quality_label = 'HD' WHERE quality LIKE '%HD%' AND quality_label IS NULL;
-- UPDATE stream_db.content SET quality_label = '4K' WHERE quality LIKE '%4K%' AND quality_label IS NULL;
-- UPDATE stream_db.content SET quality_label = 'BluRay' WHERE quality LIKE '%BluRay%' AND quality_label IS NULL;
-- UPDATE stream_db.content SET quality_label = 'WEB-DL' WHERE quality LIKE '%WEB%' AND quality_label IS NULL;
-- UPDATE stream_db.content SET quality_label = '1080p' WHERE quality LIKE '%1080%' AND quality_label IS NULL;
-- UPDATE stream_db.content SET quality_label = '720p' WHERE quality LIKE '%720%' AND quality_label IS NULL;