# Production Deployment Steps - StreamDB Web Series Management

## Prerequisites
- SSH access to production server (45.93.8.197)
- Local codebase at: `G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB`
- Production path: `/var/www/streamdb_root/data/www/streamdb.online`

## Step 1: Connect to Production Server

```bash
ssh root@45.93.8.197
```

## Step 2: Create Backup

```bash
cd /var/www/streamdb_root/data/www/streamdb.online
mkdir -p backups/$(date +%Y%m%d_%H%M%S)
cp -r src/components/admin src/types server/routes backups/$(date +%Y%m%d_%H%M%S)/
echo "Backup created in backups/$(date +%Y%m%d_%H%M%S)/"
```

## Step 3: Deploy Files (Run from Local Machine)

Open a new terminal/command prompt on your local machine and run these commands:

```bash
# Deploy AddTitleForm.tsx
scp "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\src\components\admin\AddTitleForm.tsx" root@45.93.8.197:"/var/www/streamdb_root/data/www/streamdb.online/src/components/admin/AddTitleForm.tsx"

# Deploy WebSeriesManager.tsx
scp "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\src\components\admin\WebSeriesManager.tsx" root@45.93.8.197:"/var/www/streamdb_root/data/www/streamdb.online/src/components/admin/WebSeriesManager.tsx"

# Deploy admin types
scp "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\src\types\admin.ts" root@45.93.8.197:"/var/www/streamdb_root/data/www/streamdb.online/src/types/admin.ts"

# Deploy episodes API
scp "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\server\routes\episodes.js" root@45.93.8.197:"/var/www/streamdb_root/data/www/streamdb.online/server/routes/episodes.js"
```

## Step 4: Restart Backend Services (On Production Server)

```bash
cd /var/www/streamdb_root/data/www/streamdb.online
pm2 restart index
pm2 restart webhook-server
pm2 status
```

## Step 5: Rebuild Frontend (On Production Server)

```bash
npm run build
```

## Step 6: Verify Deployment

```bash
# Check PM2 status
pm2 status

# Check recent logs
pm2 logs index --lines 20

# Check if build was successful
ls -la dist/
```

## Step 7: Test Functionality

1. **Visit Admin Panel**: https://streamdb.online/admin
2. **Test Add New Content**:
   - Go to "Add New Content" tab
   - Select "Web Series" as type
   - Verify "Seasons & Episodes" section appears between "Basic Details" and "Poster & Thumbnails"
   - Test adding seasons and episodes

3. **Test All Web-Series Tab**:
   - Go to "All Web-Series" tab
   - Verify table layout with responsive design
   - Test CRUD operations (Edit, Delete, Publish/Unpublish, Feature/Unfeature)
   - Test search and filtering

4. **Test Mobile Responsiveness**:
   - Test on mobile devices or browser dev tools
   - Check breakpoints: 320px, 768px, 1024px

## Step 8: Sync Local Codebase (Optional)

If you want to keep local and production in sync:

```bash
# From local machine, pull any production-specific changes
# (Only if there were any manual fixes made on production)
scp root@45.93.8.197:"/var/www/streamdb_root/data/www/streamdb.online/src/components/admin/AddTitleForm.tsx" "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\src\components\admin\AddTitleForm.tsx"
```

## Rollback Instructions (If Needed)

```bash
ssh root@45.93.8.197
cd /var/www/streamdb_root/data/www/streamdb.online

# List available backups
ls -la backups/

# Restore from backup (replace TIMESTAMP with actual backup folder)
cp -r backups/[TIMESTAMP]/* .

# Restart services
pm2 restart index
pm2 restart webhook-server

# Rebuild frontend
npm run build
```

## Troubleshooting

### If PM2 shows restart loops:
```bash
pm2 logs index --lines 50
pm2 logs webhook-server --lines 50
```

### If build fails:
```bash
npm install
npm run build
```

### If database issues:
```bash
# Check database connection
node -e "const db = require('./server/config/database'); db.testConnection();"
```

## Success Indicators

✅ PM2 shows both processes running without restart loops
✅ Frontend build completes successfully
✅ Admin panel loads at https://streamdb.online/admin
✅ "Seasons & Episodes" section appears for Web Series in "Add New Content"
✅ "All Web-Series" tab shows table layout with CRUD operations
✅ Mobile responsiveness works across all breakpoints
✅ Database operations persist correctly

## Post-Deployment Monitoring

Monitor for the first 30 minutes after deployment:
- Check PM2 logs: `pm2 logs --lines 100`
- Monitor server resources: `htop`
- Test admin panel functionality periodically
- Check for any error reports

## Contact Information

If issues arise during deployment:
- Check the DEPLOYMENT_SUMMARY.md for detailed technical information
- Review the Issues.txt file for known solutions
- All changes are backward compatible and preserve existing functionality
