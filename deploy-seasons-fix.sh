#!/bin/bash

# StreamDB Seasons & Episodes Fix Deployment Script
# This script deploys the fixes for season and episode management

echo "🚀 StreamDB Seasons & Episodes Fix Deployment"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the StreamDB root directory"
    exit 1
fi

print_status "Starting deployment of seasons & episodes fixes..."

# Step 1: Backup current files
print_status "Creating backup of current files..."
mkdir -p backups/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

if [ -f "server/routes/episodes.js" ]; then
    cp server/routes/episodes.js "$BACKUP_DIR/episodes.js.backup"
    print_success "Backed up episodes.js"
fi

if [ -f "src/services/apiService.js" ]; then
    cp src/services/apiService.js "$BACKUP_DIR/apiService.js.backup"
    print_success "Backed up apiService.js"
fi

if [ -f "src/components/admin/EpisodeManager.tsx" ]; then
    cp src/components/admin/EpisodeManager.tsx "$BACKUP_DIR/EpisodeManager.tsx.backup"
    print_success "Backed up EpisodeManager.tsx"
fi

# Step 2: Verify the fixes are in place
print_status "Verifying fixes are in place..."

# Check if episodes.js has authentication
if grep -q "authenticateToken" server/routes/episodes.js; then
    print_success "✓ Authentication middleware found in episodes.js"
else
    print_error "✗ Authentication middleware missing in episodes.js"
    exit 1
fi

# Check if episodes.js has validation
if grep -q "seasonValidation" server/routes/episodes.js; then
    print_success "✓ Validation middleware found in episodes.js"
else
    print_error "✗ Validation middleware missing in episodes.js"
    exit 1
fi

# Check if apiService.js has updated endpoints
if grep -q "content/.*seasons/.*episodes" src/services/apiService.js; then
    print_success "✓ Updated API endpoints found in apiService.js"
else
    print_error "✗ Updated API endpoints missing in apiService.js"
    exit 1
fi

# Step 3: Install dependencies if needed
print_status "Checking dependencies..."
if [ -f "server/package.json" ]; then
    cd server
    if ! npm list express-validator >/dev/null 2>&1; then
        print_status "Installing express-validator..."
        npm install express-validator
        print_success "express-validator installed"
    else
        print_success "express-validator already installed"
    fi
    cd ..
fi

# Step 4: Build frontend
print_status "Building frontend..."
npm run build
if [ $? -eq 0 ]; then
    print_success "Frontend build completed successfully"
else
    print_error "Frontend build failed"
    exit 1
fi

# Step 5: Test backend syntax
print_status "Testing backend syntax..."
cd server
node -c routes/episodes.js
if [ $? -eq 0 ]; then
    print_success "Backend syntax check passed"
else
    print_error "Backend syntax check failed"
    exit 1
fi
cd ..

print_success "All checks passed! Ready for deployment."

echo ""
echo "📋 Next Steps:"
echo "1. Deploy to production server:"
echo "   - Copy server/routes/episodes.js to production"
echo "   - Copy dist/ folder to production"
echo "   - Restart PM2: pm2 restart index"
echo ""
echo "2. Test the fixes:"
echo "   - Go to Admin Panel → All Web-Series"
echo "   - Try adding a new season"
echo "   - Try adding episodes to seasons"
echo ""
echo "3. Monitor logs:"
echo "   - Check PM2 logs: pm2 logs index"
echo "   - Check browser console for any errors"
echo ""

print_status "Deployment preparation completed successfully!"
