#!/bin/bash

# Deploy Episodes Fix Script
echo "🚀 Deploying Episodes Database Fix to Production Server..."

# SSH into production server and apply fixes
ssh root@*********** << 'EOF'
cd /var/www/streamdb_root/data/www/streamdb.online

# Backup current episodes.js
cp server/routes/episodes.js server/routes/episodes.js.backup.$(date +%Y%m%d_%H%M%S)

# Create the fixed episodes.js file
cat > server/routes/episodes.js << 'EPISODES_EOF'
/**
 * Episodes and Seasons Management API Routes
 * Handles CRUD operations for episodes and seasons of web series
 */
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// Validation middleware for seasons
const seasonValidation = [
  body('seasonNumber').isInt({ min: 1 }).withMessage('Season number must be a positive integer'),
  body('title').optional().isLength({ max: 255 }).withMessage('Title must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('posterUrl').optional().isURL().withMessage('Poster URL must be a valid URL'),
];

// Validation middleware for episodes
const episodeValidation = [
  body('episodeNumber').isInt({ min: 1 }).withMessage('Episode number must be a positive integer'),
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Title is required and must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('secureVideoLinks').optional().isLength({ max: 2000 }).withMessage('Video links must be less than 2000 characters'),
  body('runtime').optional().isLength({ max: 20 }).withMessage('Runtime must be less than 20 characters'),
  body('airDate').optional().isISO8601().withMessage('Air date must be a valid date'),
  body('thumbnailUrl').optional().isURL().withMessage('Thumbnail URL must be a valid URL'),
];

/**
 * Get all seasons for a content item
 * GET /api/episodes/content/:contentId/seasons
 */
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;

    const [seasonsRows] = await db.execute(`
      SELECT * FROM seasons
      WHERE content_id = ?
      ORDER BY season_number ASC
    `, [contentId]);

    // db.execute returns [rows, fields], so we destructure to get rows
    const seasons = seasonsRows || [];

    // Get episodes for each season
    for (let season of seasons || []) {
      const [episodesRows] = await db.execute(`
        SELECT * FROM episodes
        WHERE season_id = ?
        ORDER BY episode_number ASC
      `, [season.id]);

      // db.execute returns [rows, fields], so we destructure to get rows
      season.episodes = episodesRows || [];
    }

    res.json({
      success: true,
      data: seasons
    });

  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch seasons'
    });
  }
});

/**
 * Create new season
 * POST /api/episodes/content/:contentId/seasons
 */
router.post('/content/:contentId/seasons', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    console.log('Season creation request received:', {
      contentId: req.params.contentId,
      body: req.body,
      user: req.user?.username
    });

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Season validation errors:', errors.array());
      console.log('Request body that failed validation:', req.body);
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array(),
        receivedData: req.body
      });
    }

    const { contentId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    console.log('Creating season for content:', contentId, 'Season:', seasonNumber);

    // Check if season number already exists for this content
    try {
      const [existingSeasonRows] = await db.execute(
        'SELECT id FROM seasons WHERE content_id = ? AND season_number = ?',
        [contentId, seasonNumber]
      );

      // Handle mysql2 result format properly
      // db.execute returns [rows, fields], so we destructure to get rows
      const existingSeason = existingSeasonRows || [];

      if (existingSeason.length > 0) {
        console.log('Season already exists:', existingSeason);
        return res.status(400).json({
          success: false,
          error: 'Validation Error',
          message: 'Season number already exists for this content'
        });
      }

      // Generate unique ID for season
      const seasonId = `season_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      console.log('Inserting new season with ID:', seasonId);

      await db.execute(`
        INSERT INTO seasons (
          id, content_id, season_number, title, description, poster_url, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [seasonId, contentId, seasonNumber, title || null, description || null, posterUrl || null]);

      console.log('Season created successfully:', seasonId);

      // Update content total seasons count
      await db.execute(`
        UPDATE content SET
          total_seasons = (SELECT COUNT(*) FROM seasons WHERE content_id = ?),
          updated_at = NOW()
        WHERE id = ?
      `, [contentId, contentId]);

      res.status(201).json({
        success: true,
        message: 'Season created successfully',
        data: {
          id: seasonId,
          contentId,
          seasonNumber,
          title,
          description,
          posterUrl,
          episodes: []
        }
      });

    } catch (dbError) {
      console.error('Database error during season creation:', dbError);
      
      // Handle specific database errors
      if (dbError.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({
          success: false,
          error: 'Validation Error',
          message: 'Season number already exists for this content'
        });
      }
      
      throw dbError; // Re-throw for general error handler
    }

  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create season'
    });
  }
});
EPISODES_EOF

echo "✅ Episodes.js file updated with database fixes"

# Restart the backend server to apply changes
echo "🔄 Restarting backend server..."
pm2 restart index

echo "✅ Backend server restarted"
echo "🎉 Episodes database fix deployment completed!"

EOF

echo "✅ Deployment script executed successfully"
