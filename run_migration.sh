#!/bin/bash

# Database Migration Script for Quality Label Feature
# Based on .env configuration: stream_db database

echo "🔧 Running Database Migration for Quality Label Feature..."
echo "Database: stream_db"
echo "User: stream_db_admin"
echo ""

# Run the migration using the credentials from .env
mysql -h localhost -u stream_db_admin -p'Ohno@U2foundme' stream_db < database_migration_add_quality_label.sql

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully!"
    echo "The quality_label column has been added to the content table."
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Test the 'Add New Content' form with Video Quality Label section"
    echo "2. Test the 'Manage Content' form to edit existing content quality labels"
    echo "3. Verify quality labels appear on homepage content cards"
else
    echo "❌ Migration failed. Please check your database credentials and connection."
    echo "Make sure MySQL is running and the database exists."
fi