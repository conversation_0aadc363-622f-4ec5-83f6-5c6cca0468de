const db = require('../config/database');

// Function to handle multiple section IDs
async function handleContentSectionMappings(contentId, sections) {
  if (!sections || !Array.isArray(sections)) {
    return;
  }

  // Delete existing mappings
  await db.execute('DELETE FROM content_section_mappings WHERE content_id = ?', [contentId]);

  // Insert new mappings
  for (const sectionId of sections) {
    if (sectionId) {
      await db.execute(
        'INSERT INTO content_section_mappings (content_id, section_id) VALUES (?, ?)',
        [contentId, sectionId]
      );
    }
  }
}

// Function to get content sections for a content item
async function getContentSections(contentId) {
  const result = await db.execute(`
    SELECT cs.id, cs.name, cs.slug, cs.color, cs.icon
    FROM content_sections cs
    JOIN content_section_mappings csm ON cs.id = csm.section_id
    WHERE csm.content_id = ?
    ORDER BY cs.display_order
  `, [contentId]);
  
  return Array.isArray(result) ? result : [];
}

// Function to get content with sections included
async function getContentWithSections(baseQuery, params = []) {
  const content = await db.execute(baseQuery, params);
  
  if (!Array.isArray(content) || content.length === 0) {
    return content;
  }

  // Get sections for each content item
  for (let item of content) {
    item.sections = await getContentSections(item.id);
    item.section_ids = item.sections.map(s => s.id);
  }

  return content;
}

module.exports = {
  handleContentSectionMappings,
  getContentSections,
  getContentWithSections
};
