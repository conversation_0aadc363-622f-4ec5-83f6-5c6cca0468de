/**
 * Safe Storage Utility - Handles localStorage conflicts with browser extensions
 * Provides fallback to memory storage when localStorage is blocked or returns undefined
 */

class SafeStorage {
  private memoryStorage: Map<string, string> = new Map();
  private isLocalStorageAvailable: boolean;

  constructor() {
    this.isLocalStorageAvailable = this.checkLocalStorage();
  }

  private checkLocalStorage(): boolean {
    try {
      const test = '__storage_test__';
      window.localStorage.setItem(test, test);
      const result = window.localStorage.getItem(test);
      window.localStorage.removeItem(test);
      
      // Check if setItem returned undefined (browser extension conflict)
      if (result === null) {
        console.warn('localStorage test failed - browser extension conflict detected');
        return false;
      }
      
      return true;
    } catch (e) {
      console.warn('localStorage not available or blocked by extension:', e);
      return false;
    }
  }

  setItem(key: string, value: any): void {
    try {
      const serialized = JSON.stringify(value);
      
      if (this.isLocalStorageAvailable) {
        const result = window.localStorage.setItem(key, serialized);
        
        // Check for browser extension conflict (setItem returns undefined)
        if (result === undefined) {
          console.warn('localStorage.setItem returned undefined - browser extension conflict detected');
          console.warn('Falling back to memory storage for key:', key);
          this.memoryStorage.set(key, serialized);
          return;
        }
        
        // Verify the item was actually stored
        const verification = window.localStorage.getItem(key);
        if (verification !== serialized) {
          console.warn('localStorage verification failed - using memory fallback');
          this.memoryStorage.set(key, serialized);
        }
      } else {
        this.memoryStorage.set(key, serialized);
      }
    } catch (error) {
      console.warn('Storage setItem failed, using memory fallback:', error);
      this.memoryStorage.set(key, JSON.stringify(value));
    }
  }

  getItem(key: string): any {
    try {
      let value: string | null = null;
      
      if (this.isLocalStorageAvailable) {
        value = window.localStorage.getItem(key);
      }
      
      // Fallback to memory storage if localStorage failed
      if (value === null && this.memoryStorage.has(key)) {
        value = this.memoryStorage.get(key) || null;
      }
      
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('Storage getItem failed:', error);
      
      // Try memory storage as last resort
      try {
        const memValue = this.memoryStorage.get(key);
        return memValue ? JSON.parse(memValue) : null;
      } catch (memError) {
        console.warn('Memory storage getItem also failed:', memError);
        return null;
      }
    }
  }

  removeItem(key: string): void {
    try {
      if (this.isLocalStorageAvailable) {
        window.localStorage.removeItem(key);
      }
      this.memoryStorage.delete(key);
    } catch (error) {
      console.warn('Storage removeItem failed:', error);
      this.memoryStorage.delete(key);
    }
  }

  clear(): void {
    try {
      if (this.isLocalStorageAvailable) {
        window.localStorage.clear();
      }
      this.memoryStorage.clear();
    } catch (error) {
      console.warn('Storage clear failed:', error);
      this.memoryStorage.clear();
    }
  }

  // Get storage info for debugging
  getStorageInfo(): { type: string; available: boolean; memoryKeys: string[] } {
    return {
      type: this.isLocalStorageAvailable ? 'localStorage' : 'memory',
      available: this.isLocalStorageAvailable,
      memoryKeys: Array.from(this.memoryStorage.keys())
    };
  }

  // Test storage functionality
  testStorage(): boolean {
    try {
      const testKey = '__safe_storage_test__';
      const testValue = { test: true, timestamp: Date.now() };
      
      this.setItem(testKey, testValue);
      const retrieved = this.getItem(testKey);
      this.removeItem(testKey);
      
      const success = retrieved && retrieved.test === true;
      console.log('Storage test result:', success ? 'PASSED' : 'FAILED');
      
      return success;
    } catch (error) {
      console.error('Storage test failed:', error);
      return false;
    }
  }
}

// Create singleton instance
const safeStorage = new SafeStorage();

// Test storage on initialization
safeStorage.testStorage();

// Log storage info for debugging
console.log('Safe Storage initialized:', safeStorage.getStorageInfo());

export default safeStorage;

// Export class for custom instances if needed
export { SafeStorage };

// Utility functions for common operations
export const storageUtils = {
  // Save user preferences
  saveUserPrefs(prefs: Record<string, any>): void {
    safeStorage.setItem('userPreferences', prefs);
  },

  // Get user preferences
  getUserPrefs(): Record<string, any> {
    return safeStorage.getItem('userPreferences') || {};
  },

  // Save session data
  saveSessionData(data: Record<string, any>): void {
    safeStorage.setItem('sessionData', data);
  },

  // Get session data
  getSessionData(): Record<string, any> {
    return safeStorage.getItem('sessionData') || {};
  },

  // Save form data (for recovery)
  saveFormData(formId: string, data: Record<string, any>): void {
    safeStorage.setItem(`formData_${formId}`, data);
  },

  // Get form data
  getFormData(formId: string): Record<string, any> {
    return safeStorage.getItem(`formData_${formId}`) || {};
  },

  // Clear form data
  clearFormData(formId: string): void {
    safeStorage.removeItem(`formData_${formId}`);
  }
};
