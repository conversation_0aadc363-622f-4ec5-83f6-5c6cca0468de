# StreamDB.online Reverse Proxy Configuration
# Deploy to: /etc/nginx/sites-available/streamdb.online on 91.208.197.50
#
# INSTRUCTIONS:
# 1. Copy this entire configuration to your reverse proxy server (91.208.197.50)
# 2. Save as: /etc/nginx/sites-available/streamdb.online
# 3. Enable with: ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/
# 4. Test with: nginx -t
# 5. Reload with: systemctl reload nginx

# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# Main website - HTTP to HTTPS redirect
server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main website - HTTPS
server {
    listen 443 ssl http2;
    server_name streamdb.online www.streamdb.online;
    
    # SSL Configuration (Cloudflare handles certificates)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https: https://pertawee.net https://al5sm.com; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;
    
    # Hide server information
    server_tokens off;
    
    # Rate limiting
    limit_req zone=general burst=20 nodelay;
    
    # Proxy settings for backend
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # Proxy timeouts
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    
    # API routes with rate limiting - Direct to Node.js
    location /api/ {
        limit_req zone=api burst=10 nodelay;

        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Admin panel routes with extra security - Direct to Node.js
    location /admin {
        limit_req zone=admin burst=5 nodelay;

        # Additional security for admin
        add_header X-Admin-Access "Restricted" always;

        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Static files and main application - Direct to Node.js
    location / {
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://***********:3001;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://***********:3001/api/health;
        access_log off;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Logging
    access_log /var/log/nginx/streamdb_access.log;
    error_log /var/log/nginx/streamdb_error.log;
}

# FastPanel subdomain - HTTP to HTTPS redirect
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    return 301 https://$server_name$request_uri;
}

# FastPanel subdomain - HTTPS
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL Configuration (same as main site)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers for FastPanel (relaxed for functionality)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "same-origin" always;

    # FastPanel API endpoints - no rate limiting for better functionality
    location /api/ {
        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;

        # Disable buffering for API responses
        proxy_buffering off;
        proxy_request_buffering off;

        # Extended timeouts for API calls
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # FastPanel file manager and terminal endpoints
    location ~ ^/(local|www|api/files|api/terminal) {
        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;

        # Disable buffering for file operations
        proxy_buffering off;
        proxy_request_buffering off;

        # Extended timeouts for file operations
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # Increase body size for file uploads
        client_max_body_size 1G;
    }

    # Proxy to FastPanel on backend server (with light rate limiting)
    location / {
        # Light rate limiting only for general pages
        limit_req zone=admin burst=50 nodelay;

        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;

        # Disable proxy buffering for real-time responses
        proxy_buffering off;
        proxy_request_buffering off;

        # Longer timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Logging
    access_log /var/log/nginx/fastpanel_access.log;
    error_log /var/log/nginx/fastpanel_error.log;
}
