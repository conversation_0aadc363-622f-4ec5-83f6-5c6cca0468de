import { Link } from "react-router-dom";
import { MediaItem } from "@/types/media";
import { scrollToTop } from "@/utils/scrollToTop";
import SafeImage from "@/components/SafeImage";

export default function CardGrid({ items }: { items: MediaItem[] }) {
  // Safety check for items array
  if (!items || !Array.isArray(items)) {
    console.warn('CardGrid: items is not an array', items);
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4"> {/* Reduced gaps for more compact layout */}
        <div className="col-span-full text-center text-muted-foreground py-8">
          <div className="text-4xl mb-2">🎬</div>
          <p>No content available</p>
        </div>
      </div>
    );
  }

  // Filter out any invalid items
  const validItems = items.filter(item => {
    if (!item || typeof item !== 'object') {
      console.warn('CardGrid: Invalid item found', item);
      return false;
    }
    if (!item.id) {
      console.warn('CardGrid: Item missing ID', item);
      return false;
    }
    return true;
  });

  if (validItems.length === 0) {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4"> {/* Reduced gaps for more compact layout */}
        <div className="col-span-full text-center text-muted-foreground py-8">
          <div className="text-4xl mb-2">🎬</div>
          <p>No valid content available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4"> {/* Reduced gaps for more compact layout */}
      {validItems.map((item, index) => {
        try {
          // Enhanced genre processing
          let processedGenres = [];
          if (item.genres) {
            if (Array.isArray(item.genres)) {
              processedGenres = item.genres.filter(g => g && typeof g === 'string' && g.trim().length > 0);
            } else if (typeof item.genres === 'string') {
              processedGenres = item.genres.split(',').map(g => g.trim()).filter(g => g.length > 0);
            }
          }

          // Process quality tags - PRODUCTION READY
          let processedQuality = [];
          
          // Enhanced quality processing with multiple fallbacks
          if (item.quality) {
            if (Array.isArray(item.quality)) {
              processedQuality = item.quality.filter(q => q && typeof q === 'string' && q.trim().length > 0);
            } else if (typeof item.quality === 'string' && item.quality.trim().length > 0) {
              try {
                // Try to parse as JSON first
                const parsed = JSON.parse(item.quality);
                if (Array.isArray(parsed)) {
                  processedQuality = parsed.filter(q => q && typeof q === 'string' && q.trim().length > 0);
                }
              } catch {
                // If not JSON, treat as comma-separated string
                processedQuality = item.quality.split(',').map(q => q.trim()).filter(q => q.length > 0);
              }
            }
          }

          // Process quality label for homepage display
          let qualityLabel = '';
          if (item.qualityLabel || item.quality_label) {
            qualityLabel = item.qualityLabel || item.quality_label;
          } else if (item.quality && typeof item.quality === 'string') {
            // Check if quality field contains a single quality label (not comma-separated)
            const qualityParts = item.quality.split(',').map(q => q.trim());
            if (qualityParts.length === 1 && ['HD', '4K', '1080p', '720p', 'WEB-DL', 'BluRay', 'CAM', 'HDTS', 'HDTC'].includes(qualityParts[0])) {
              qualityLabel = qualityParts[0];
            }
          }

          const safeItem = {
            id: item.id || `item-${index}`,
            title: item.title || 'Untitled',
            image: item.image || item.posterUrl || '/placeholder-image.jpg',
            genres: processedGenres,
            year: item.year || 'Unknown',
            quality: processedQuality,
            qualityLabel: qualityLabel
          };

          return (
            <Link
              to={`/content/${safeItem.id}`}
              key={`${safeItem.id}-${index}`}
              onClick={scrollToTop}
              className="card-grid-item group bg-card shadow-lg hover:scale-[1.04] transition-transform duration-200 hover:ring-2 hover:ring-primary/60 flex flex-col rounded-2xl overflow-hidden"
            >
            {/* Poster image */}
            <div className="relative w-full" style={{ aspectRatio: "2/3", background: "#191d25" }}>
              <SafeImage
                src={safeItem.image}
                alt={safeItem.title}
                className="w-full h-full object-cover bg-[#191d25] rounded-none"
                style={{
                  aspectRatio: "2/3",
                  display: "block"
                }}
                placeholder={
                  <div className="w-full h-full flex items-center justify-center bg-muted">
                    <div className="text-center text-muted-foreground">
                      <div className="text-3xl mb-2">🎬</div>
                      <div className="text-xs">Loading...</div>
                    </div>
                  </div>
                }
              />
              
              {/* Quality Label - Top Right Corner (Primary) */}
              {safeItem.qualityLabel && (
                <div className="absolute top-2 right-2">
                  <span
                    className="px-2 py-1 text-xs font-bold bg-gradient-to-r from-yellow-500 to-orange-600 text-white rounded-md shadow-lg border border-white/30 backdrop-blur-sm"
                    style={{
                      textShadow: '0 1px 3px rgba(0,0,0,0.9)',
                      fontSize: '10px',
                      lineHeight: '1.3',
                      letterSpacing: '0.5px'
                    }}
                  >
                    {safeItem.qualityLabel.toUpperCase()}
                  </span>
                </div>
              )}

              {/* Fallback Quality Tags - Top Right Corner (if no quality label) */}
              {!safeItem.qualityLabel && processedQuality && processedQuality.length > 0 && (
                <div className="absolute top-2 right-2 flex flex-wrap gap-1 max-w-[60%]">
                  {processedQuality.slice(0, 2).map((qualityTag, qIndex) => (
                    <span
                      key={qIndex}
                      className="px-1.5 py-0.5 text-[10px] font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded shadow-lg border border-white/20 backdrop-blur-sm"
                      style={{
                        textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                        fontSize: '9px',
                        lineHeight: '1.2'
                      }}
                    >
                      {qualityTag.toUpperCase()}
                    </span>
                  ))}
                  {processedQuality.length > 2 && (
                    <span
                      className="px-1.5 py-0.5 text-[10px] font-bold bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded shadow-lg border border-white/20 backdrop-blur-sm"
                      style={{
                        textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                        fontSize: '9px',
                        lineHeight: '1.2'
                      }}
                    >
                      +{processedQuality.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>
            {/* Title */}
            <span
              className="card-title"
              title={safeItem.title}
            >
              {safeItem.title}
            </span>
            {/* Genres & year, modern layout */}
            <div
              className="flex flex-col gap-1.5 items-center justify-center px-2 pb-2 mb-1"
              style={{
                background: "none",
                borderTop: "1px solid rgba(100,100,100,0.10)"
              }}
            >
              <span className="card-genres">
                {safeItem.genres.length > 0 ? safeItem.genres.join(", ") : "No genres"}
              </span>
              <span className="card-year">{safeItem.year}</span>
            </div>
          </Link>
        );
        } catch (error) {
          console.error('Error rendering card item:', error, item);
          return null;
        }
      }).filter(Boolean)}
    </div>
  );
}