<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StreamDB - Free Movies & TV Series Online | Watch HD Content</title>
    <meta name="description" content="Watch free movies and TV series online in HD quality. StreamDB offers the latest movies, popular TV shows, and classic content. No registration required." />
    <meta name="author" content="StreamDB" />
    <meta name="keywords" content="free movies, tv series, watch online, streaming, HD movies, latest movies, tv shows, entertainment" />
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="1 days" />
    <meta name="distribution" content="global" />
    <meta name="rating" content="general" />

    <!-- Favicon - Comprehensive Implementation for Google Search -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="48x48" href="/favicon-48x48.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="theme-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="msapplication-TileImage" content="/android-chrome-512x512.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://streamdb.online/" />
    <meta property="og:title" content="StreamDB - Free Movies & TV Series Online" />
    <meta property="og:description" content="Watch free movies and TV series online in HD quality. Latest releases and classic content available." />
    <meta property="og:image" content="https://streamdb.online/android-chrome-512x512.png" />
    <meta property="og:image:width" content="512" />
    <meta property="og:image:height" content="512" />
    <meta property="og:image:type" content="image/png" />
    <meta property="og:site_name" content="StreamDB" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://streamdb.online/" />
    <meta property="twitter:title" content="StreamDB - Free Movies & TV Series Online" />
    <meta property="twitter:description" content="Watch free movies and TV series online in HD quality. Latest releases and classic content available." />
    <meta property="twitter:image" content="https://streamdb.online/android-chrome-512x512.png" />
    <meta name="twitter:site" content="@StreamDB" />
    <meta name="twitter:creator" content="@StreamDB" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://api.themoviedb.org" />
    <link rel="preconnect" href="https://image.tmdb.org" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://streamdb.online/" />

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "StreamDB",
      "url": "https://streamdb.online",
      "description": "Free online streaming platform for movies and TV series",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://streamdb.online/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "StreamDB",
        "url": "https://streamdb.online"
      }
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
