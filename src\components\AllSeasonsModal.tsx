import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Calendar, Clock, Loader2 } from "lucide-react";
import { MediaItem, Season, Episode } from "@/types/media";
import apiService from "@/services/apiService";

interface AllSeasonsModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: MediaItem | null;
  onEpisodeSelect: (videoLinks: string, episodeTitle: string) => void;
}

export default function AllSeasonsModal({
  isOpen,
  onClose,
  content,
  onEpisodeSelect
}: AllSeasonsModalProps) {
  const [seasons, setSeasons] = useState<Season[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedSeasonId, setSelectedSeasonId] = useState<string>("");
  const [selectedEpisodeId, setSelectedEpisodeId] = useState<string>("");

  // Load seasons and episodes from database
  const loadSeasons = async () => {
    if (!content || content.type !== 'series') return;

    try {
      setLoading(true);
      const result = await apiService.getContentSeasons(content.id);

      if (result.success && result.data) {
        const seasonsData = result.data.map(season => ({
          ...season,
          episodes: season.episodes || []
        }));
        
        setSeasons(seasonsData);

        // Auto-select first season only if no season is currently selected
        if (seasonsData.length > 0 && !selectedSeasonId) {
          setSelectedSeasonId(seasonsData[0].id);
        }
      } else {
        console.error('Failed to load seasons:', result.message);
        setSeasons([]);
      }
    } catch (error) {
      console.error('Error loading seasons:', error);
      setSeasons([]);
    } finally {
      setLoading(false);
    }
  };

  // Load seasons when modal opens
  useEffect(() => {
    if (isOpen && content) {
      setSelectedSeasonId(""); // Reset selection when modal opens
      setSelectedEpisodeId(""); // Reset episode selection
      setSeasons([]); // Clear previous data
      loadSeasons();
    }
  }, [isOpen, content?.id]); // Only depend on isOpen and content.id, not the entire content object

  // Handle season selection
  const handleSeasonSelect = (seasonId: string) => {
    setSelectedSeasonId(seasonId);
    setSelectedEpisodeId(""); // Reset episode selection when season changes
  };

  // Handle episode selection from dropdown
  const handleEpisodeSelectFromDropdown = (episodeId: string) => {
    setSelectedEpisodeId(episodeId);

    // Find the selected episode and its video links
    const selectedSeason = seasons.find(season => season.id === selectedSeasonId);
    const selectedEpisode = selectedSeason?.episodes?.find(episode => episode.id === episodeId);

    if (selectedEpisode) {
      const videoLinks = selectedEpisode.secure_video_links || selectedEpisode.secureVideoLinks;
      const seasonNum = selectedSeason?.season_number || selectedSeason?.seasonNumber;
      const episodeNum = selectedEpisode.episode_number || selectedEpisode.episode;
      const episodeTitle = `S${seasonNum}E${episodeNum}: ${selectedEpisode.title}`;

      if (videoLinks) {
        onEpisodeSelect(videoLinks, episodeTitle);
        onClose();

        // Scroll to video player after a short delay to allow modal to close
        setTimeout(() => {
          const videoPlayerElement = document.querySelector('.video-player-container');
          if (videoPlayerElement) {
            videoPlayerElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }, 300);
      }
    }
  };

  if (!content || content.type !== 'series') {
    return null;
  }

  const totalSeasons = seasons.length;
  const totalEpisodes = seasons.reduce((total, season) => total + (season.episodes?.length || 0), 0);
  const selectedSeason = seasons.find(season => season.id === selectedSeasonId);
  const selectedEpisode = selectedSeason?.episodes?.find(episode => episode.id === selectedEpisodeId);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden bg-background/95 backdrop-blur-sm border-border/50"> {/* Fixed Issue #2: Reduced modal size to match Episodes section style */}
        <DialogHeader className="pb-4 border-b border-border/50">
          <DialogTitle className="flex items-center gap-3 text-xl font-bold">
            <div className="p-2 rounded-lg bg-primary/10">
              <Play className="w-6 h-6 text-primary" />
            </div>
            <div>
              <div className="text-xl font-bold">{content.title}</div>
              <div className="text-sm font-normal text-muted-foreground">All Seasons & Episodes</div>
            </div>
          </DialogTitle>
          <div className="flex items-center gap-6 text-sm text-muted-foreground mt-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-primary"></div>
              <span>{totalSeasons} Season{totalSeasons !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-secondary"></div>
              <span>{totalEpisodes} Episode{totalEpisodes !== 1 ? 's' : ''}</span>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Loading seasons...</span>
          </div>
        ) : seasons.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Play className="w-12 h-12 text-muted-foreground/50 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Seasons Available</h3>
            <p className="text-muted-foreground">This series doesn't have any seasons yet.</p>
          </div>
        ) : (
          <div className="p-6 space-y-6"> {/* Fixed Issue #2: Changed to dropdown-based design matching Episodes section */}
            <h2 className="stdb-heading text-2xl mb-4"
                style={{
                  color: "#e6cb8e",
                  fontFamily: "'Koulen', Impact, Arial, sans-serif",
                  fontWeight: 500,
                  letterSpacing: "0.04em",
                  textTransform: "uppercase",
                  textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                }}>
              Episodes
            </h2>

            <div className="space-y-4">
              {/* Season Selector - Matching Episodes section design */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Select Season
                </label>
                <Select value={selectedSeasonId} onValueChange={handleSeasonSelect}>
                  <SelectTrigger className="w-full bg-background/50 border-border/50 hover:border-primary/50 transition-colors">
                    <SelectValue placeholder="Choose a season..." />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border/50">
                    {seasons.map((season) => (
                      <SelectItem
                        key={season.id}
                        value={season.id}
                        className="hover:bg-primary/10 hover:text-primary transition-colors"
                      >
                        Season {season.season_number || season.seasonNumber}
                        {season.title && ` - ${season.title}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Episode Selector - Matching Episodes section design */}
              {selectedSeasonId && (
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Select Episode
                  </label>
                  <Select value={selectedEpisodeId} onValueChange={handleEpisodeSelectFromDropdown}>
                    <SelectTrigger className="w-full bg-background/50 border-border/50 hover:border-primary/50 transition-colors">
                      <SelectValue placeholder="Choose an episode..." />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border/50 max-h-60">
                      {selectedSeason?.episodes?.map((episode) => (
                        <SelectItem
                          key={episode.id}
                          value={episode.id}
                          className="hover:bg-primary/10 hover:text-primary transition-colors"
                          disabled={!episode.secure_video_links && !episode.secureVideoLinks}
                        >
                          <div className="flex flex-col items-start">
                            <span className="font-medium">
                              E{episode.episode_number || episode.episode}: {episode.title}
                            </span>
                            {episode.description && (
                              <span className="text-xs text-muted-foreground line-clamp-1 mt-1">
                                {episode.description}
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Selected Episode Info - Matching Episodes section design */}
              {selectedEpisodeId && selectedEpisode && (
                <div className="mt-4 p-4 bg-primary/10 border border-primary/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-primary">
                        S{selectedSeason?.season_number || selectedSeason?.seasonNumber}E{selectedEpisode.episode_number || selectedEpisode.episode}: {selectedEpisode.title}
                      </p>
                      {selectedEpisode.description && (
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {selectedEpisode.description}
                        </p>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleEpisodeSelectFromDropdown(selectedEpisodeId)}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground ml-4"
                    >
                      <Play className="w-4 h-4 mr-2 fill-current" />
                      Watch Now
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
