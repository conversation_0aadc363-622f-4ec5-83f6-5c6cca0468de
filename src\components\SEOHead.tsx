import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  type?: 'website' | 'article' | 'video.movie' | 'video.tv_show';
  noIndex?: boolean;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'StreamDB - Free Movies & TV Series Online',
  description = 'Watch free movies and TV series online at StreamDB. Discover the latest releases, popular classics, and trending content in HD quality.',
  keywords = ['free movies', 'tv series', 'streaming', 'watch online', 'HD movies', 'latest movies', 'popular series'],
  image = 'https://streamdb.online/android-chrome-512x512.png',
  type = 'website',
  noIndex = false
}) => {
  const location = useLocation();
  const baseUrl = 'https://streamdb.online';
  const currentUrl = `${baseUrl}${location.pathname}`;

  useEffect(() => {
    // Update document title
    document.title = title;

    // Remove existing meta tags
    const existingMetas = document.querySelectorAll('meta[data-seo="true"]');
    existingMetas.forEach(meta => meta.remove());

    // Remove existing structured data
    const existingStructuredData = document.querySelectorAll('script[type="application/ld+json"][data-seo="true"]');
    existingStructuredData.forEach(script => script.remove());

    // Create enhanced meta tags for better SEO
    const metaTags = [
      { name: 'description', content: description },
      { name: 'keywords', content: keywords.join(', ') },
      { name: 'robots', content: noIndex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1' },
      { name: 'googlebot', content: noIndex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1' },
      { name: 'language', content: 'English' },
      { name: 'author', content: 'StreamDB' },
      { name: 'revisit-after', content: '1 days' },
      { name: 'distribution', content: 'global' },
      { name: 'rating', content: 'general' },

      // Open Graph Enhanced
      { property: 'og:type', content: type },
      { property: 'og:url', content: currentUrl },
      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      { property: 'og:image', content: image },
      { property: 'og:image:width', content: '512' },
      { property: 'og:image:height', content: '512' },
      { property: 'og:image:type', content: 'image/png' },
      { property: 'og:site_name', content: 'StreamDB' },
      { property: 'og:locale', content: 'en_US' },

      // Twitter Enhanced
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:url', content: currentUrl },
      { name: 'twitter:title', content: title },
      { name: 'twitter:description', content: description },
      { name: 'twitter:image', content: image },
      { name: 'twitter:site', content: '@StreamDB' },
      { name: 'twitter:creator', content: '@StreamDB' },

      // Additional SEO Meta Tags
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
      { name: 'format-detection', content: 'telephone=no' },
    ];

    // Add meta tags to head
    metaTags.forEach(tag => {
      const meta = document.createElement('meta');
      meta.setAttribute('data-seo', 'true');
      
      if ('name' in tag) {
        meta.name = tag.name;
      } else if ('property' in tag) {
        meta.setAttribute('property', tag.property);
      }
      
      meta.content = tag.content;
      document.head.appendChild(meta);
    });

    // Add canonical link
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.rel = 'canonical';
      document.head.appendChild(canonical);
    }
    canonical.href = currentUrl;

    // Add enhanced structured data for website
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "StreamDB",
      "alternateName": "StreamDB - Free Movies & TV Series",
      "description": "Free movies and TV series streaming platform with HD quality content",
      "url": baseUrl,
      "sameAs": [
        "https://streamdb.online"
      ],
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${baseUrl}/search?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "StreamDB",
        "url": baseUrl,
        "logo": {
          "@type": "ImageObject",
          "url": `${baseUrl}/android-chrome-512x512.png`,
          "width": 512,
          "height": 512
        }
      },
      "mainEntity": {
        "@type": "WebPage",
        "@id": currentUrl,
        "url": currentUrl,
        "name": title,
        "description": description,
        "isPartOf": {
          "@type": "WebSite",
          "@id": baseUrl
        }
      }
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-seo', 'true');
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

  }, [title, description, keywords, image, type, noIndex, currentUrl]);

  return null;
};

export default SEOHead;