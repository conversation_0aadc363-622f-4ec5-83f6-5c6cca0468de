#!/usr/bin/env node

/**
 * Automatic SEO Update Script for StreamDB
 * Runs daily to update sitemap and notify search engines
 * 
 * Features:
 * - Generates fresh sitemap with current content
 * - Notifies Google and Bing about updates
 * - Logs activity for monitoring
 * - Can be scheduled with cron
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';
import { generateSitemap } from './generate-sitemap.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const siteUrl = 'https://streamdb.online';
const logDir = path.join(__dirname, '..', 'logs');
const logFile = path.join(logDir, 'seo-updates.log');

// Ensure log directory exists
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Log message to file and console
 */
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  
  fs.appendFileSync(logFile, logMessage);
}

/**
 * Notify search engines about sitemap update
 */
async function notifySearchEngines() {
  log('🔔 Notifying search engines about sitemap update...');
  
  const searchEngines = [
    {
      name: 'Google',
      url: `https://www.google.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
    },
    {
      name: 'Bing',
      url: `https://www.bing.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
    }
  ];

  for (const engine of searchEngines) {
    try {
      await new Promise((resolve, reject) => {
        const req = https.get(engine.url, (res) => {
          if (res.statusCode === 200) {
            log(`✅ Successfully notified ${engine.name}`);
          } else {
            log(`⚠️ ${engine.name} returned status ${res.statusCode}`);
          }
          resolve();
        });
        
        req.on('error', (err) => {
          log(`❌ Failed to notify ${engine.name}: ${err.message}`);
          resolve(); // Don't reject, continue with other engines
        });
        
        req.setTimeout(10000, () => {
          req.destroy();
          log(`⏰ Timeout notifying ${engine.name}`);
          resolve();
        });
      });
    } catch (error) {
      log(`❌ Error notifying ${engine.name}: ${error.message}`);
    }
  }
}

/**
 * Main function to run the automatic update
 */
async function runAutoUpdate() {
  try {
    log('🔄 Starting automatic SEO update...');
    
    // Generate fresh sitemap
    log('🗺️ Generating fresh sitemap...');
    await generateSitemap();
    
    // Notify search engines
    await notifySearchEngines();
    
    log('✅ Automatic SEO update completed successfully!');
    
  } catch (error) {
    log(`❌ Error during automatic SEO update: ${error.message}`);
    process.exit(1);
  }
}

// Run the update
runAutoUpdate();
