#!/bin/bash

# Reverse Proxy Flow Verification Script
# Tests: Client → Cloudflare → ************* (NGINX) → *********** (Backend)
# Domain: streamdb.online

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="streamdb.online"
PROXY_IP="*************"
BACKEND_IP="***********"
CLOUDFLARE_IPS=("************/20" "************/22" "************/22" "**********/22" "************/18" "*************/18" "************/20" "************/20" "*************/22" "************/17" "***********/15" "**********/13" "**********/14" "**********/13" "**********/22")

# Global variables for tracking issues
ISSUES_FOUND=0
SOLUTIONS=()
BACKEND_HTTP_ACCESSIBLE=false
BACKEND_DOMAIN_ACCESSIBLE=false

print_header() {
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${CYAN}                           REVERSE PROXY FLOW VERIFICATION SCRIPT${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${BLUE}Testing Flow: Client → Cloudflare → $PROXY_IP (NGINX) → $BACKEND_IP (Backend)${NC}"
    echo -e "${BLUE}Domain: $DOMAIN${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
    echo ""
}

log_issue() {
    local message="$1"
    local solution="$2"
    echo -e "${RED}❌ ISSUE: $message${NC}"
    SOLUTIONS+=("$solution")
    ((ISSUES_FOUND++))
}

log_success() {
    local message="$1"
    echo -e "${GREEN}✅ PASS: $message${NC}"
}

log_warning() {
    local message="$1"
    echo -e "${YELLOW}⚠️  WARNING: $message${NC}"
}

log_info() {
    local message="$1"
    echo -e "${BLUE}ℹ️  INFO: $message${NC}"
}

check_dependencies() {
    echo -e "${PURPLE}[1/8] Checking Required Dependencies...${NC}"
    
    local deps=("curl" "dig" "nslookup" "ping")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_issue "Missing required dependencies: ${missing_deps[*]}" "Install missing dependencies: sudo apt-get install curl dnsutils iputils-ping (Ubuntu/Debian) or equivalent for your OS"
        return 1
    fi
    
    log_success "All required dependencies are installed"
    echo ""
    return 0
}

check_dns_resolution() {
    echo -e "${PURPLE}[2/8] Checking DNS Resolution...${NC}"
    
    # Get A records for the domain
    local dns_result=$(dig +short A "$DOMAIN" 2>/dev/null)
    
    if [ -z "$dns_result" ]; then
        log_issue "DNS resolution failed for $DOMAIN" "Check DNS configuration and ensure domain is properly configured"
        echo ""
        return 1
    fi
    
    log_info "DNS A records for $DOMAIN:"
    echo "$dns_result" | while read -r ip; do
        echo -e "  ${CYAN}→ $ip${NC}"
    done
    
    # Check if any of the IPs belong to Cloudflare
    local is_cloudflare=false
    while read -r resolved_ip; do
        for cf_range in "${CLOUDFLARE_IPS[@]}"; do
            if [[ "$resolved_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                # Simple check if IP is in Cloudflare range (basic implementation)
                local cf_network=$(echo "$cf_range" | cut -d'/' -f1)
                local cf_prefix=$(echo "$cf_range" | cut -d'/' -f2)
                # For simplicity, checking if first two octets match common Cloudflare ranges
                if [[ "$resolved_ip" =~ ^104\.(1[6-9]|2[0-9]|3[01])\. ]] || [[ "$resolved_ip" =~ ^172\.6[4-9]\. ]] || [[ "$resolved_ip" =~ ^173\.245\. ]]; then
                    is_cloudflare=true
                    break
                fi
            fi
        done
    done <<< "$dns_result"
    
    if [ "$is_cloudflare" = true ]; then
        log_success "Domain resolves to Cloudflare IP addresses"
    else
        log_warning "Domain may not be using Cloudflare (IPs don't match known Cloudflare ranges)"
        log_info "This might be expected if using Cloudflare proxy with custom IPs"
    fi
    
    echo ""
    return 0
}

check_cloudflare_headers() {
    echo -e "${PURPLE}[3/8] Checking Cloudflare Integration...${NC}"
    
    # Test HTTP and HTTPS responses for Cloudflare headers
    for protocol in "http" "https"; do
        echo -e "${BLUE}Testing $protocol://$DOMAIN${NC}"
        
        local response=$(curl -s -I -m 10 "$protocol://$DOMAIN" 2>/dev/null)
        
        if [ $? -ne 0 ] || [ -z "$response" ]; then
            log_issue "$protocol://$DOMAIN is not accessible" "Check if $protocol is properly configured on Cloudflare and the reverse proxy"
            continue
        fi
        
        # Check for Cloudflare headers
        if echo "$response" | grep -qi "cf-ray\|cloudflare"; then
            log_success "$protocol - Cloudflare headers detected"
        else
            log_warning "$protocol - No Cloudflare headers found (may be hidden)"
        fi
        
        # Check response code
        local status_code=$(echo "$response" | head -n1 | awk '{print $2}')
        if [[ "$status_code" =~ ^[23] ]]; then
            log_success "$protocol - Received successful response ($status_code)"
        else
            log_issue "$protocol - Received error response ($status_code)" "Check server configuration and ensure proper response codes"
        fi
    done
    
    echo ""
    return 0
}

check_backend_protection() {
    echo -e "${PURPLE}[4/8] Checking Backend Server Protection...${NC}"

    # Test direct access to backend IP
    echo -e "${BLUE}Testing direct access to backend server ($BACKEND_IP)${NC}"

    for protocol in "http" "https"; do
        local direct_response=$(curl -s -I -m 5 "$protocol://$BACKEND_IP" 2>/dev/null)
        local curl_exit_code=$?

        if [ $curl_exit_code -eq 0 ] && [ -n "$direct_response" ]; then
            local status_code=$(echo "$direct_response" | head -n1 | awk '{print $2}')
            log_issue "Backend server ($BACKEND_IP) is directly accessible via $protocol (Status: $status_code)" "Configure firewall to block direct access to $BACKEND_IP. Only allow connections from $PROXY_IP"
            if [ "$protocol" = "http" ]; then
                BACKEND_HTTP_ACCESSIBLE=true
            fi
        else
            log_success "Backend server ($BACKEND_IP) is not directly accessible via $protocol"
        fi
    done

    # Test with domain name pointing directly to backend
    echo -e "${BLUE}Testing if domain can bypass proxy by pointing directly to backend${NC}"
    local direct_domain_test=$(curl -s -I -m 5 -H "Host: $DOMAIN" "http://$BACKEND_IP" 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$direct_domain_test" ]; then
        log_issue "Backend server responds to domain requests directly" "Configure backend server to only accept requests from reverse proxy IP ($PROXY_IP)"
        BACKEND_DOMAIN_ACCESSIBLE=true
    else
        log_success "Backend server properly rejects direct domain requests"
    fi

    echo ""
    return 0
}

check_proxy_functionality() {
    echo -e "${PURPLE}[5/8] Checking Reverse Proxy Functionality...${NC}"

    # Test if proxy server is accessible
    echo -e "${BLUE}Testing reverse proxy server ($PROXY_IP)${NC}"

    local proxy_response=$(curl -s -I -m 10 -H "Host: $DOMAIN" "http://$PROXY_IP" 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$proxy_response" ]; then
        log_issue "Reverse proxy server ($PROXY_IP) is not responding" "Check NGINX configuration and ensure the service is running on $PROXY_IP"
    else
        local status_code=$(echo "$proxy_response" | head -n1 | awk '{print $2}')
        if [[ "$status_code" =~ ^[23] ]]; then
            log_success "Reverse proxy server is responding correctly ($status_code)"
        else
            log_warning "Reverse proxy server returned status $status_code"
        fi
    fi

    # Check if proxy adds any identifying headers
    if echo "$proxy_response" | grep -qi "nginx\|server:"; then
        local server_header=$(echo "$proxy_response" | grep -i "server:" | head -n1)
        log_info "Server header detected: $server_header"
        log_warning "Consider hiding server headers for better security"
    fi

    echo ""
    return 0
}

check_ssl_certificates() {
    echo -e "${PURPLE}[6/8] Checking SSL Certificates...${NC}"

    # Check SSL certificate details
    echo -e "${BLUE}Testing SSL certificate for $DOMAIN${NC}"

    local ssl_info=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -text 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$ssl_info" ]; then
        # Check certificate issuer
        local issuer=$(echo "$ssl_info" | grep "Issuer:" | head -n1)
        echo -e "  ${CYAN}Certificate Issuer: $issuer${NC}"

        if echo "$issuer" | grep -qi "cloudflare\|let's encrypt"; then
            log_success "SSL certificate appears to be properly configured"
        else
            log_info "SSL certificate issuer: $issuer"
        fi

        # Check certificate validity
        local not_after=$(echo "$ssl_info" | grep "Not After" | head -n1)
        echo -e "  ${CYAN}Certificate Validity: $not_after${NC}"

    else
        log_issue "Unable to retrieve SSL certificate information" "Check SSL configuration on Cloudflare and ensure HTTPS is properly set up"
    fi

    echo ""
    return 0
}

check_response_consistency() {
    echo -e "${PURPLE}[7/8] Checking Response Consistency...${NC}"

    # Test multiple requests to ensure consistent responses
    echo -e "${BLUE}Testing response consistency across multiple requests${NC}"

    local responses=()
    for i in {1..3}; do
        local response=$(curl -s -w "%{http_code}" -o /dev/null -m 10 "https://$DOMAIN" 2>/dev/null)
        responses+=("$response")
        sleep 1
    done

    # Check if all responses are the same
    local first_response="${responses[0]}"
    local consistent=true

    for response in "${responses[@]}"; do
        if [ "$response" != "$first_response" ]; then
            consistent=false
            break
        fi
    done

    if [ "$consistent" = true ]; then
        log_success "Response codes are consistent across multiple requests ($first_response)"
    else
        log_warning "Inconsistent response codes detected: ${responses[*]}"
        log_info "This might indicate load balancing issues or intermittent problems"
    fi

    # Test response time
    echo -e "${BLUE}Testing response times${NC}"
    local total_time=$(curl -s -w "%{time_total}" -o /dev/null -m 10 "https://$DOMAIN" 2>/dev/null)

    if [ -n "$total_time" ]; then
        echo -e "  ${CYAN}Response time: ${total_time}s${NC}"

        # Check if response time is reasonable (without bc dependency)
        if (( $(echo "$total_time > 5.0" | awk '{print ($1 > $3)}') )); then
            log_warning "Response time is high (${total_time}s) - consider optimizing"
        else
            log_success "Response time is acceptable (${total_time}s)"
        fi
    fi

    echo ""
    return 0
}

check_security_headers() {
    echo -e "${PURPLE}[8/8] Checking Security Headers and Information Leakage...${NC}"

    # Get full headers
    local headers=$(curl -s -I -m 10 "https://$DOMAIN" 2>/dev/null)

    if [ -z "$headers" ]; then
        log_issue "Unable to retrieve headers from $DOMAIN" "Check if the website is accessible and responding to requests"
        echo ""
        return 1
    fi

    echo -e "${BLUE}Analyzing response headers for security and information leakage${NC}"

    # Check for information disclosure in headers
    local sensitive_headers=("X-Powered-By" "Server" "X-AspNet-Version" "X-AspNetMvc-Version")

    for header in "${sensitive_headers[@]}"; do
        if echo "$headers" | grep -qi "^$header:"; then
            local header_value=$(echo "$headers" | grep -i "^$header:" | head -n1)
            log_warning "Potentially sensitive header detected: $header_value"
        fi
    done

    # Check for backend IP leakage in headers
    if echo "$headers" | grep -q "$BACKEND_IP"; then
        log_issue "Backend IP ($BACKEND_IP) found in response headers" "Configure reverse proxy to remove/replace headers that contain backend IP"
    else
        log_success "No backend IP leakage detected in headers"
    fi

    if echo "$headers" | grep -q "$PROXY_IP"; then
        log_warning "Proxy IP ($PROXY_IP) found in response headers"
    else
        log_success "No proxy IP leakage detected in headers"
    fi

    # Check for security headers
    local security_headers=("Strict-Transport-Security" "X-Content-Type-Options" "X-Frame-Options" "X-XSS-Protection")

    for sec_header in "${security_headers[@]}"; do
        if echo "$headers" | grep -qi "^$sec_header:"; then
            log_success "Security header present: $sec_header"
        else
            log_info "Security header missing: $sec_header (consider adding for better security)"
        fi
    done

    echo ""
    return 0
}

print_dmca_analysis() {
    if [ "$BACKEND_HTTP_ACCESSIBLE" = true ] || [ "$BACKEND_DOMAIN_ACCESSIBLE" = true ]; then
        echo -e "${CYAN}================================================================================================${NC}"
        echo -e "${CYAN}                              🛡️  DMCA PROTECTION ANALYSIS${NC}"
        echo -e "${CYAN}================================================================================================${NC}"
        echo ""

        echo -e "${GREEN}✅ GOOD NEWS: Your Backend IP is Still Hidden from DMCA Detection${NC}"
        echo ""

        echo -e "${BLUE}🔍 Why these issues DON'T expose you to DMCA:${NC}"
        echo -e "   ${GREEN}✅${NC} DNS Resolution Protection: Domain resolves to Cloudflare IPs only"
        echo -e "   ${GREEN}✅${NC} Public Discovery: DMCA agents follow DNS, not random IP scans"
        echo -e "   ${GREEN}✅${NC} Practical Obscurity: Backend IP not publicly linked to domain"
        echo -e "   ${GREEN}✅${NC} Core Architecture: Reverse proxy flow working correctly"
        echo ""

        echo -e "${YELLOW}⚠️  HOWEVER: These Issues Create Security Vulnerabilities:${NC}"
        echo -e "   ${RED}🎯${NC} Targeted Attacks: Bypass Cloudflare if IP discovered"
        echo -e "   ${RED}📊${NC} Traffic Analysis: Advanced adversaries could trace patterns"
        echo -e "   ${RED}🔍${NC} Security Scanning: Automated scanners might find open ports"
        echo -e "   ${RED}⚡${NC} DDoS Bypass: Direct backend targeting bypasses Cloudflare"
        echo ""

        echo -e "${PURPLE}🎯 ASSESSMENT: Configuration Oversights, NOT Critical DMCA Risks${NC}"
        echo ""

        echo -e "${BLUE}✅ Primary DMCA Protection Intact:${NC}"
        echo -e "   • Domain pointing to Cloudflare (Working ✅)"
        echo -e "   • Traffic routing through proxy server (Working ✅)"
        echo -e "   • No public association between domain and backend IP (Working ✅)"
        echo -e "   • DMCA agents don't typically scan random IP ranges"
        echo ""

        echo -e "${YELLOW}🔧 Recommended Action: MEDIUM Priority${NC}"
        echo -e "   ${CYAN}→${NC} Fix for security best practices, not urgent DMCA risk"
        echo -e "   ${CYAN}→${NC} Implements defense-in-depth security strategy"
        echo -e "   ${CYAN}→${NC} Future-proofs against potential IP discovery"
        echo ""

        echo -e "${CYAN}================================================================================================${NC}"
    fi
}

print_detailed_solutions() {
    if [ "$BACKEND_HTTP_ACCESSIBLE" = true ] || [ "$BACKEND_DOMAIN_ACCESSIBLE" = true ]; then
        echo -e "${CYAN}================================================================================================${NC}"
        echo -e "${CYAN}                              🔧 DETAILED SECURITY FIXES${NC}"
        echo -e "${CYAN}================================================================================================${NC}"
        echo ""

        if [ "$BACKEND_HTTP_ACCESSIBLE" = true ]; then
            echo -e "${RED}❌ Issue #1: Backend HTTP Direct Access${NC}"
            echo -e "${BLUE}Root Cause:${NC} UFW allows port 80 from ALL sources instead of proxy only"
            echo -e "${BLUE}Current Rule:${NC} ufw allow 80/tcp (❌ Too permissive)"
            echo -e "${GREEN}Correct Rule:${NC} ufw allow from $PROXY_IP to any port 80"
            echo ""
            echo -e "${YELLOW}Fix Commands:${NC}"
            echo -e "   ${CYAN}ssh root@$BACKEND_IP${NC}"
            echo -e "   ${CYAN}ufw delete allow 80/tcp${NC}"
            echo -e "   ${CYAN}ufw allow from $PROXY_IP to any port 80${NC}"
            echo -e "   ${CYAN}ufw reload${NC}"
            echo ""
        fi

        if [ "$BACKEND_DOMAIN_ACCESSIBLE" = true ]; then
            echo -e "${RED}❌ Issue #2: Backend Responds to Domain Requests${NC}"
            echo -e "${BLUE}Root Cause:${NC} NGINX accepts domain requests on backend server"
            echo -e "${BLUE}Current Config:${NC} server_name includes $DOMAIN"
            echo -e "${GREEN}Solution:${NC} Restrict NGINX to only accept proxy requests"
            echo ""
            echo -e "${YELLOW}Fix Options:${NC}"
            echo -e "   ${CYAN}Option A:${NC} Remove domain from server_name (backend IP only)"
            echo -e "   ${CYAN}Option B:${NC} Add IP-based access control in NGINX"
            echo -e "   ${CYAN}Option C:${NC} Configure NGINX to only trust proxy headers"
            echo ""
        fi

        echo -e "${GREEN}🎯 Expected Results After Fix:${NC}"
        echo -e "   ${GREEN}✅${NC} Direct IP access blocked (curl http://$BACKEND_IP = timeout)"
        echo -e "   ${GREEN}✅${NC} Domain requests blocked (curl -H \"Host: $DOMAIN\" http://$BACKEND_IP = blocked)"
        echo -e "   ${GREEN}✅${NC} Proxy access working (https://$DOMAIN = working)"
        echo -e "   ${GREEN}✅${NC} Admin panel working (https://$DOMAIN/admin = working)"
        echo ""

        echo -e "${CYAN}================================================================================================${NC}"
    fi
}

print_summary() {
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${CYAN}                                    VERIFICATION SUMMARY${NC}"
    echo -e "${CYAN}================================================================================================${NC}"

    if [ $ISSUES_FOUND -eq 0 ]; then
        echo -e "${GREEN}🎉 EXCELLENT! No critical issues found with your reverse proxy setup.${NC}"
        echo -e "${GREEN}Your backend server ($BACKEND_IP) appears to be properly hidden.${NC}"
    else
        echo -e "${RED}⚠️  Found $ISSUES_FOUND issue(s) that need attention:${NC}"
        echo ""

        local counter=1
        for solution in "${SOLUTIONS[@]}"; do
            echo -e "${YELLOW}Issue #$counter Solution:${NC}"
            echo -e "  ${CYAN}$solution${NC}"
            echo ""
            ((counter++))
        done

        # Print detailed analysis for backend access issues
        print_dmca_analysis
        print_detailed_solutions
    fi

    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${BLUE}Verification completed at $(date)${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
}

# Main execution
main() {
    print_header

    # Run all checks
    check_dependencies || exit 1
    check_dns_resolution
    check_cloudflare_headers
    check_backend_protection
    check_proxy_functionality
    check_ssl_certificates
    check_response_consistency
    check_security_headers

    # Print summary
    print_summary

    # Exit with appropriate code
    if [ $ISSUES_FOUND -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Run the script
main "$@"
