# 🚀 Production Deployment Guide - Ubuntu Server

## 🏗️ **Architecture Understanding:**

### **Current Setup:**
- **Local Development**: Windows machine (where we're editing code)
- **Production Server**: Ubuntu Linux (45.93.8.197)
- **Reverse Proxy**: <PERSON><PERSON><PERSON> (91.208.197.50)
- **Database**: MySQL 8.0 with socket connection on Ubuntu
- **Process Manager**: PM2
- **Application Path**: `/var/www/streamdb_onl_usr/data/www/streamdb.online`

## ✅ **Configuration Fixed for Production:**

### **1. Database Configuration** ✅ CORRECTED
- **Restored socket connection** for Ubuntu production server
- **Path**: `/var/run/mysqld/mysqld.sock` (correct for Ubuntu)
- **Connection**: Socket-based (most secure for same-server setup)

### **2. New API Routes Added** ✅ READY
- **TMDB Backend Route**: `server/routes/tmdb.js`
- **OMDB Backend Route**: `server/routes/omdb.js`
- **Route Registration**: Added to `server/index.js`

## 🚀 **Deployment Steps for Ubuntu Production Server:**

### **Step 1: Upload Files to Production**
Upload these modified files to the Ubuntu server:

```bash
# Files to upload to: /var/www/streamdb_onl_usr/data/www/streamdb.online/
server/.env                    # Database config fixed
server/index.js               # Routes added
server/routes/tmdb.js         # New TMDB backend route
server/routes/omdb.js         # New OMDB backend route
src/services/omdbService.ts   # Enhanced OMDB service
src/components/admin/AddTitleForm.tsx           # Backend API integration
src/components/admin/TMDBSearchDialog.tsx      # Backend API integration
```

### **Step 2: SSH into Production Server**
```bash
ssh root@45.93.8.197
# or your SSH method
```

### **Step 3: Navigate to Application Directory**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
```

### **Step 4: Install Dependencies (if new)**
```bash
cd server
npm install  # Install any new dependencies
```

### **Step 5: Test Database Connection**
```bash
# Test if MySQL socket is accessible
ls -la /var/run/mysqld/mysqld.sock

# Test database connection
mysql -u stream_db_admin -p stream_db
# Enter password: Ohno@U2foundme
```

### **Step 6: Restart PM2 Process**
```bash
# Check current PM2 status
pm2 status

# Restart the application
pm2 restart streamdb
# or
pm2 restart all

# Check logs for any errors
pm2 logs streamdb
```

### **Step 7: Verify Deployment**
Test these endpoints:
```bash
# Health check
curl https://streamdb.online/api/health

# TMDB API test
curl https://streamdb.online/api/tmdb/test

# OMDB API test  
curl https://streamdb.online/api/omdb/test

# Admin panel
curl -I https://streamdb.online/admin
```

## 🔍 **Troubleshooting:**

### **If 502 Error Persists:**

1. **Check PM2 Status:**
```bash
pm2 status
pm2 logs streamdb --lines 50
```

2. **Check MySQL Service:**
```bash
systemctl status mysql
# If stopped: systemctl start mysql
```

3. **Check Socket Permissions:**
```bash
ls -la /var/run/mysqld/mysqld.sock
# Should be accessible by the app user
```

4. **Check Application Logs:**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
tail -f server.log
```

5. **Test Database Connection Manually:**
```bash
cd server
node -e "
require('dotenv').config();
const mysql = require('mysql2/promise');
mysql.createConnection({
  socketPath: '/var/run/mysqld/mysqld.sock',
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
}).then(() => console.log('✅ DB Connected')).catch(console.error);
"
```

## 🎯 **Expected Results After Deployment:**

- ✅ **502 Error Resolved** - Database connection restored
- ✅ **TMDB API Fixed** - Brooklyn Nine-Nine (ID: 48891) fetches correctly
- ✅ **OMDB API Enhanced** - All form fields populated from OMDB data
- ✅ **Admin Panel Accessible** - https://streamdb.online/admin works
- ✅ **Backend APIs Available** - /api/tmdb and /api/omdb routes active

## 📋 **Environment Variables Check:**
Ensure these are set correctly in production `.env`:
```bash
NODE_ENV=production
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=stream_db
DB_USER=stream_db_admin
DB_PASSWORD=Ohno@U2foundme
VITE_TMDB_API_KEY=d42e51fef0cd194377f0df77218b08cb
VITE_OMDB_API_KEY=eb3aa9e2
```

## 🔄 **Deployment Checklist:**
- [ ] Files uploaded to Ubuntu server
- [ ] Database socket connection verified
- [ ] PM2 process restarted
- [ ] Health endpoint responding
- [ ] Admin panel accessible
- [ ] TMDB/OMDB APIs working
- [ ] Brooklyn Nine-Nine test successful

The 502 error should be resolved once the corrected configuration is deployed to the Ubuntu production server!