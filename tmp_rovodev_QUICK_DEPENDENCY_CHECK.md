# 🚨 QUICK DEPENDENCY CHECK

## **Most Likely Issue: Missing Dependencies**

### **Run these commands on production server:**

```bash
# SSH into server
ssh root@45.93.8.197

# Navigate to server directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Install missing dependencies
npm install axios node-fetch

# Check if server starts manually
node index.js

# If working, restart PM2
pm2 restart streamdb-online
```

### **If axios/node-fetch are missing, the server will crash immediately!**

**Check PM2 logs first to confirm the exact error:**
```bash
pm2 logs streamdb-online --lines 20
```