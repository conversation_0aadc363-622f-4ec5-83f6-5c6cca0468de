{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving code quality while maintaining existing functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.tsx", "src/**/*.ts", "src/**/*.js", "src/**/*.jsx", "server/**/*.js", "server/**/*.ts", "*.js", "*.ts", "*.tsx", "*.jsx"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code in the changed files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells such as long methods, large classes, duplicate code, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check adherence to JavaScript/TypeScript/React best practices\n4. **Readability**: Suggest improvements for code clarity and documentation\n5. **Maintainability**: Identify areas that could be refactored for easier maintenance\n6. **Performance**: Suggest optimizations that could improve performance without breaking functionality\n\nProvide specific, actionable suggestions with code examples where helpful. Maintain the existing functionality while improving code quality. Focus on the most impactful improvements first."}}