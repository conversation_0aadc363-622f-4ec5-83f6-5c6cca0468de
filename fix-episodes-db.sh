#!/bin/bash

# Fix Episodes Database Result Handling
echo "🔧 Applying database result handling fixes to episodes.js..."

# Apply the fixes using sed commands
sed -i.backup \
  -e 's/const seasonsResult = await db\.execute(/const [seasonsRows] = await db.execute(/g' \
  -e 's/const seasons = seasonsResult || \[\];/const seasons = seasonsRows || [];/g' \
  -e 's/const episodesResult = await db\.execute(/const [episodesRows] = await db.execute(/g' \
  -e 's/season\.episodes = episodesResult || \[\];/season.episodes = episodesRows || [];/g' \
  -e 's/const existingSeasonResult = await db\.execute(/const [existingSeasonRows] = await db.execute(/g' \
  -e 's/const existingSeason = existingSeasonResult || \[\];/const existingSeason = existingSeasonRows || [];/g' \
  -e 's/const seasonResult = await db\.execute(/const [seasonRows] = await db.execute(/g' \
  -e 's/const season = seasonResult || \[\];/const season = seasonRows || [];/g' \
  -e 's/const existingEpisodeResult = await db\.execute(/const [existingEpisodeRows] = await db.execute(/g' \
  -e 's/const existingEpisode = existingEpisodeResult || \[\];/const existingEpisode = existingEpisodeRows || [];/g' \
  -e 's/const episodeResult = await db\.execute(/const [episodeRows] = await db.execute(/g' \
  -e 's/const episode = episodeResult || \[\];/const episode = episodeRows || [];/g' \
  -e 's/const conflictingEpisodeResult = await db\.execute(/const [conflictingEpisodeRows] = await db.execute(/g' \
  -e 's/const conflictingEpisode = conflictingEpisodeResult || \[\];/const conflictingEpisode = conflictingEpisodeRows || [];/g' \
  -e 's/\/\/ db\.execute returns rows directly, not \[rows, fields\]/\/\/ db.execute returns [rows, fields], so we destructure to get rows/g' \
  server/routes/episodes.js

echo "✅ Database result handling fixes applied"
echo "🔄 Restarting backend server..."
pm2 restart index
echo "✅ Backend server restarted"
