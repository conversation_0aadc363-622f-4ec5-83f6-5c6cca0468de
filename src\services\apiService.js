/**
 * API Service for Database Integration
 * Handles all communication with the backend API
 * NO CREDENTIALS EXPOSED - All security handled server-side
 */

// API Configuration - NO sensitive data here
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'  // Production: same domain
  : 'http://localhost:3001/api';  // Development: local server

// Check if we're in a preview/build environment without backend
const isPreviewMode = window.location.port === '4173' || window.location.hostname === 'localhost';

// Session management using secure HTTP-only cookies and database storage
// No client-side token storage for enhanced security

// API Client Class - Now uses secure HTTP-only cookies for authentication
class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    // No client-side token storage - authentication handled via secure HTTP-only cookies
  }

  // Session management (secure) - No client-side token storage
  async checkAuthStatus() {
    try {
      const response = await this.request('/auth/status');
      return response && response.authenticated;
    } catch (error) {
      return false;
    }
  }

  async refreshSession() {
    try {
      const response = await this.request('/auth/refresh', { method: 'POST' });
      return response && response.success;
    } catch (error) {
      return false;
    }
  }

  // HTTP request helper with JWT authentication
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    // If in preview mode without backend, return mock data
    if (isPreviewMode && !window.location.search.includes('use-api=true')) {
      return this.getMockResponse(endpoint, options.method || 'GET');
    }

    const token = this.getAuthToken();

    const config = {
      credentials: 'include', // Include HTTP-only cookies for authentication
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // Add Authorization header if token is available
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, config);

      // Handle authentication errors
      if (response.status === 401) {
        console.warn('Authentication required - session may have expired');
        // Optionally redirect to login page
        // window.location.href = '/admin/login';
        throw new Error('Authentication required');
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      // In preview mode, fallback to mock data
      if (isPreviewMode) {
        console.warn('API failed, falling back to mock data');
        return this.getMockResponse(endpoint, options.method || 'GET');
      }
      throw error;
    }
  }

  // Mock response for preview mode
  getMockResponse(endpoint, method) {
    console.log(`Mock API response for ${method} ${endpoint}`);

    if (endpoint.includes('/content')) {
      return {
        success: true,
        data: [
          {
            id: "mock-1",
            title: "Sample Movie",
            description: "This is a sample movie for preview",
            year: 2024,
            genres: ["Action", "Adventure"],
            quality: ["4K", "HDR", "1080p"],
            type: "movie",
            image: "/placeholder-image.jpg",
            coverImage: "/placeholder-image.jpg",
            createdAt: new Date().toISOString(),
            isPublished: true,
            isFeatured: true,
            addToCarousel: true
          },
          {
            id: "mock-2",
            title: "Sample Series",
            description: "This is a sample series for preview",
            year: 2024,
            genres: ["Drama", "Thriller"],
            quality: ["HD", "720p"],
            type: "series",
            image: "/placeholder-image.jpg",
            coverImage: "/placeholder-image.jpg",
            createdAt: new Date().toISOString(),
            isPublished: true,
            isFeatured: false,
            addToCarousel: true
          }
        ]
      };
    }

    if (endpoint.includes('/sections')) {
      // Mock sections data
      if (endpoint.includes('/content')) {
        // Mock section content - same as regular content but with quality tags
        return {
          success: true,
          data: [
            {
              id: "mock-section-1",
              title: "Featured Movie",
              description: "This is a featured movie for homepage",
              year: 2024,
              genres: ["Action", "Adventure"],
              quality: ["4K", "HDR", "1080p"],
              type: "movie",
              image: "/placeholder-image.jpg",
              coverImage: "/placeholder-image.jpg",
              createdAt: new Date().toISOString(),
              isPublished: true,
              isFeatured: true,
              addToCarousel: true
            },
            {
              id: "mock-section-2",
              title: "Popular Series",
              description: "This is a popular series for homepage",
              year: 2024,
              genres: ["Drama", "Thriller"],
              quality: ["HD", "720p"],
              type: "series",
              image: "/placeholder-image.jpg",
              coverImage: "/placeholder-image.jpg",
              createdAt: new Date().toISOString(),
              isPublished: true,
              isFeatured: false,
              addToCarousel: true
            }
          ],
          total: 2
        };
      } else {
        // Mock sections list
        return {
          success: true,
          data: [
            {
              id: 1,
              name: "Movies",
              slug: "movies",
              icon: "movie",
              color: "#e6cb8e",
              show_on_homepage: true,
              max_items_homepage: 8,
              display_order: 1
            },
            {
              id: 2,
              name: "Web Series",
              slug: "web-series",
              icon: "tv",
              color: "#e6cb8e",
              show_on_homepage: true,
              max_items_homepage: 8,
              display_order: 2
            }
          ]
        };
      }
    }

    if (endpoint.includes('/auth/status')) {
      return { authenticated: false };
    }

    if (endpoint.includes('/tracking') || endpoint.includes('/security-logs')) {
      return { success: true, message: 'Mock tracking logged' };
    }

    return { success: true, data: [] };
  }

  // Authentication methods - Now using secure HTTP-only cookies
  async login(credentials) {
    try {
      // Handle both object and individual parameters for backward compatibility
      const loginData = typeof credentials === 'object' && credentials.username
        ? { username: credentials.username, password: credentials.password }
        : { username: credentials, password: arguments[1] };

      const response = await this.request('/auth/login', {
        method: 'POST',
        body: JSON.stringify(loginData)
      });

      // Store JWT token in sessionStorage for compatibility with sectionsAPI and contentAPI
      if (response.token) {
        const sessionData = {
          token: response.token,
          user: response.user,
          expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
          createdAt: Date.now()
        };

        // Use unified session storage method
        this.storeSession(sessionData);
      }

      // No client-side token storage - authentication handled via secure HTTP-only cookies
      return { success: true, ...response };
    } catch (error) {
      console.error('Login API error:', error);
      return {
        success: false,
        error: error.message || 'Authentication failed',
        details: error
      };
    }
  }

  async logout() {
    try {
      await this.request('/auth/logout', { method: 'POST' });
    } catch (error) {
      // Silent fail - user is likely already logged out
    }
    // Clear session using unified method
    this.clearSession();
    // No client-side token cleanup needed - server handles cookie invalidation
  }

  async verifySession() {
    try {
      const response = await this.request('/auth/verify');
      return response.user;
    } catch (error) {
      return null;
    }
  }

  async changePassword(currentPassword, newPassword) {
    return await this.request('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({ currentPassword, newPassword })
    });
  }

  // Content management methods
  async getContent(filters = {}) {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value);
      }
    });

    const queryString = params.toString();
    const endpoint = `/content${queryString ? `?${queryString}` : ''}`;

    console.log('[API Service] Getting content with filters:', filters);
    console.log('[API Service] Request endpoint:', endpoint);
    const response = await this.request(endpoint);
    console.log('[API Service] Content response:', response?.success ? `${response.data?.length || 0} items` : typeof response);

    // Enhanced response processing to ensure genres are properly handled
    if (response && response.success && Array.isArray(response.data)) {
      response.data = response.data.map(item => this.processContentItem(item));
    }

    return response;
  }

  // Helper method to process individual content items and ensure proper field formatting
  processContentItem(item) {
    if (!item) return item;

    // Process genres field to ensure it's always an array
    let processedGenres = [];
    if (item.genres) {
      if (Array.isArray(item.genres)) {
        processedGenres = item.genres.filter(g => g && typeof g === 'string' && g.trim().length > 0);
      } else if (typeof item.genres === 'string') {
        try {
          // Try to parse as JSON first
          const parsed = JSON.parse(item.genres);
          if (Array.isArray(parsed)) {
            processedGenres = parsed.filter(g => g && typeof g === 'string' && g.trim().length > 0);
          }
        } catch {
          // If JSON parsing fails, treat as comma-separated string
          processedGenres = item.genres.split(',').map(g => g.trim()).filter(g => g.length > 0);
        }
      }
    }

    // Process quality field to ensure it's always an array
    let processedQuality = [];
    if (item.quality) {
      if (Array.isArray(item.quality)) {
        processedQuality = item.quality.filter(q => q && typeof q === 'string' && q.trim().length > 0);
      } else if (typeof item.quality === 'string') {
        try {
          // Try to parse as JSON first
          const parsed = JSON.parse(item.quality);
          if (Array.isArray(parsed)) {
            processedQuality = parsed.filter(q => q && typeof q === 'string' && q.trim().length > 0);
          }
        } catch {
          // If JSON parsing fails, treat as comma-separated string
          processedQuality = item.quality.split(',').map(q => q.trim()).filter(q => q.length > 0);
        }
      }
    }

    // Debug logging for field processing (only in development)
    if (process.env.NODE_ENV === 'development' && item.id) {
      console.log(`[ApiService] Processing fields for ${item.id}:`, {
        genres: { original: item.genres, processed: processedGenres, type: typeof item.genres },
        quality: { original: item.quality, processed: processedQuality, type: typeof item.quality }
      });
    }

    return {
      ...item,
      genres: processedGenres,
      quality: processedQuality
    };
  }

  async getContentById(id) {
    const response = await this.request(`/content/${id}`);

    // Process the single content item to ensure proper genre formatting
    if (response && response.success && response.data) {
      response.data = this.processContentItem(response.data);
    }

    return response;
  }

  async createContent(contentData) {
    return await this.request('/content', {
      method: 'POST',
      body: JSON.stringify(contentData)
    });
  }

  async updateContent(id, contentData) {
    return await this.request(`/content/${id}`, {
      method: 'PUT',
      body: JSON.stringify(contentData)
    });
  }

  async deleteContent(id) {
    return await this.request(`/content/${id}`, {
      method: 'DELETE'
    });
  }

  async bulkContentOperation(action, contentIds, data = {}) {
    try {
      const response = await this.request('/content/bulk', {
        method: 'POST',
        body: JSON.stringify({
          action: action,
          contentIds: contentIds,
          data: data
        })
      });
      return response;
    } catch (error) {
      console.error('Error in bulk content operation:', error);
      throw error;
    }
  }

  async bulkCreateContent(contentItems) {
    return await this.request('/content/bulk-create', {
      method: 'POST',
      body: JSON.stringify({ items: contentItems })
    });
  }

  // Episodes and Seasons management methods
  async getContentSeasons(contentId) {
    return await this.request(`/episodes/content/${contentId}/seasons`);
  }

  async getAllEpisodes() {
    return await this.request('/episodes/episodes');
  }

  async getAllEpisodes() {
    return await this.request('/episodes/episodes');
  }

  async createSeason(contentId, seasonData) {
    return await this.request(`/episodes/content/${contentId}/seasons`, {
      method: 'POST',
      body: JSON.stringify(seasonData)
    });
  }

  async createEpisode(contentId, seasonId, episodeData) {
    try {
      const response = await this.request(`/episodes/content/${contentId}/seasons/${seasonId}/episodes`, {
        method: 'POST',
        body: JSON.stringify(episodeData)
      });
      
      // Ensure consistent response format
      if (response && typeof response === 'object') {
        return {
          success: response.success !== false,
          data: response.data || null,
          message: response.message || (response.success ? 'Episode created successfully' : 'Failed to create episode'),
          error: response.error || null
        };
      }
      
      return { success: false, message: 'Invalid response format', data: null };
    } catch (error) {
      console.error('API Service - Create Episode Error:', error);
      return {
        success: false,
        message: error.message || 'Failed to create episode',
        error: error.message,
        data: null
      };
    }
  }

  async updateEpisode(contentId, seasonId, episodeId, episodeData) {
    return await this.request(`/episodes/content/${contentId}/seasons/${seasonId}/episodes/${episodeId}`, {
      method: 'PUT',
      body: JSON.stringify(episodeData)
    });
  }

  async deleteEpisode(contentId, seasonId, episodeId) {
    return await this.request(`/episodes/content/${contentId}/seasons/${seasonId}/episodes/${episodeId}`, {
      method: 'DELETE'
    });
  }

  async updateSeason(contentId, seasonId, seasonData) {
    return await this.request(`/episodes/content/${contentId}/seasons/${seasonId}`, {
      method: 'PUT',
      body: JSON.stringify(seasonData)
    });
  }

  async deleteSeason(contentId, seasonId) {
    return await this.request(`/episodes/content/${contentId}/seasons/${seasonId}`, {
      method: 'DELETE'
    });
  }

  // Category methods
  async getCategories(activeOnly = true, type = null) {
    const params = new URLSearchParams();
    if (activeOnly) params.append('active_only', 'true');
    if (type) params.append('type', type);
    
    const queryString = params.toString();
    const endpoint = `/categories${queryString ? `?${queryString}` : ''}`;
    
    return await this.request(endpoint);
  }

  async getCategoryById(id) {
    return await this.request(`/categories/${id}`);
  }

  async createCategory(categoryData) {
    return await this.request('/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    });
  }

  async updateCategory(id, categoryData) {
    return await this.request(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData)
    });
  }

  async deleteCategory(id) {
    return await this.request(`/categories/${id}`, {
      method: 'DELETE'
    });
  }

  async getContentByCategory(slug, page = 1, limit = 20) {
    return await this.request(`/categories/${slug}/content?page=${page}&limit=${limit}`);
  }

  // File upload methods
  async uploadImage(file, optimize = true) {
    const formData = new FormData();
    formData.append('image', file);

    return await this.request(`/upload/image?optimize=${optimize}`, {
      method: 'POST',
      headers: {
        // Remove Content-Type to let browser set it with boundary
        // Authentication handled via HTTP-only cookies
      },
      body: formData
    });
  }

  async uploadImages(files, optimize = true) {
    const formData = new FormData();
    files.forEach(file => formData.append('images', file));

    return await this.request(`/upload/images?optimize=${optimize}`, {
      method: 'POST',
      headers: {
        // Authentication handled via HTTP-only cookies
      },
      body: formData
    });
  }

  async uploadSubtitle(file) {
    const formData = new FormData();
    formData.append('subtitle', file);

    return await this.request('/upload/subtitle', {
      method: 'POST',
      headers: {
        // Authentication handled via HTTP-only cookies
      },
      body: formData
    });
  }

  async deleteFile(type, filename) {
    return await this.request(`/upload/${type}/${filename}`, {
      method: 'DELETE'
    });
  }

  // Admin methods
  async getDashboardStats() {
    return await this.request('/admin/dashboard');
  }

  async getUsers() {
    return await this.request('/admin/users');
  }

  async updateUserStatus(userId, isActive) {
    return await this.request(`/admin/users/${userId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ is_active: isActive })
    });
  }

  async getSecurityLogs(page = 1, limit = 50, filters = {}) {
    const params = new URLSearchParams({ page, limit });
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value);
    });

    return await this.request(`/admin/security-logs?${params.toString()}`);
  }

  async exportContent(type = null, publishedOnly = false) {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    if (publishedOnly) params.append('published_only', 'true');

    const response = await fetch(`${this.baseURL}/admin/export/content?${params.toString()}`, {
      credentials: 'include', // Include HTTP-only cookies for authentication
      headers: {
        // Authentication handled via HTTP-only cookies
      }
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    // Handle file download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `content_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  async getSystemHealth() {
    return await this.request('/admin/health');
  }

  // Secure embed link methods (NO LINKS EXPOSED)
  async getSecureEmbedLink(contentId) {
    return await this.request(`/embed/${contentId}`);
  }

  // Session data encryption (for compatibility with sectionsAPI)
  encryptSessionData(data) {
    try {
      const jsonString = JSON.stringify(data);
      // Simple XOR encryption (matches sectionsAPI implementation)
      let encrypted = '';
      const key = 'StreamDB_Session_Key_2024';

      for (let i = 0; i < jsonString.length; i++) {
        encrypted += String.fromCharCode(
          jsonString.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }

      return btoa(encrypted);
    } catch (error) {
      console.error('Failed to encrypt session data:', error);
      return JSON.stringify(data); // Fallback to plain JSON
    }
  }

  // Session data decryption
  decryptSessionData(encryptedData) {
    try {
      // Try direct JSON parse first (for backward compatibility)
      try {
        return JSON.parse(encryptedData);
      } catch {
        // If that fails, try decryption
        const decrypted = atob(encryptedData);
        let decryptedString = '';
        const key = 'StreamDB_Session_Key_2024';

        for (let i = 0; i < decrypted.length; i++) {
          decryptedString += String.fromCharCode(
            decrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length)
          );
        }

        return JSON.parse(decryptedString);
      }
    } catch (error) {
      console.error('Failed to decrypt session data:', error);
      return null;
    }
  }

  // Get current auth token
  getAuthToken() {
    try {
      const sessionData = sessionStorage.getItem('streamdb_auth_session');
      if (!sessionData) return null;

      const parsed = JSON.parse(sessionData);
      return parsed.token || null;
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Store session data
  storeSession(sessionData) {
    try {
      const encryptedData = this.encryptSessionData(sessionData);
      sessionStorage.setItem('streamdb_auth_session', encryptedData);
      return true;
    } catch (error) {
      console.error('Failed to store session:', error);
      return false;
    }
  }

  // Clear session
  clearSession() {
    try {
      sessionStorage.removeItem('streamdb_auth_session');
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.getAuthToken();
  }

  // Get current user data
  getCurrentUser() {
    try {
      const sessionData = sessionStorage.getItem('streamdb_auth_session');
      if (!sessionData) return null;

      const parsedData = this.decryptSessionData(sessionData);
      if (!parsedData) return null;

      // Check if session is expired
      if (parsedData.expiresAt && Date.now() > parsedData.expiresAt) {
        this.clearSession();
        return null;
      }

      return parsedData.user || null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Utility methods
  async testConnection() {
    try {
      const response = await fetch(`${this.baseURL}/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Search methods
  async searchContent(query, filters = {}) {
    return await this.getContent({
      search: query,
      ...filters
    });
  }

  // Homepage content methods
  async getHomepageContent() {
    const [movies, series, featured] = await Promise.all([
      this.getContent({ type: 'movie', published: true, limit: 20, sort_by: 'updated_at', sort_order: 'desc' }),
      this.getContent({ type: 'series', published: true, limit: 20, sort_by: 'updated_at', sort_order: 'desc' }),
      this.getContent({ featured: true, published: true, limit: 10, sort_by: 'updated_at', sort_order: 'desc' })
    ]);

    return {
      movies: movies.data || [],
      series: series.data || [],
      featured: featured.data || []
    };
  }

  // Category-specific content
  async getMoviesByCategory(category, page = 1) {
    return await this.getContent({
      type: 'movie',
      category,
      published: true,
      page,
      limit: 20
    });
  }

  async getSeriesByCategory(category, page = 1) {
    return await this.getContent({
      type: 'series',
      category,
      published: true,
      page,
      limit: 20
    });
  }

  // Sections management methods
  async getSections(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.active_only) queryParams.append('active_only', 'true');
    if (params.include_categories) queryParams.append('include_categories', 'true');

    const queryString = queryParams.toString();
    const endpoint = `/sections${queryString ? `?${queryString}` : ''}`;

    return await this.request(endpoint);
  }

  async getSectionById(id) {
    return await this.request(`/sections/${id}`);
  }

  async createSection(sectionData) {
    return await this.request('/sections', {
      method: 'POST',
      body: JSON.stringify(sectionData)
    });
  }

  async updateSection(id, sectionData) {
    return await this.request(`/sections/${id}`, {
      method: 'PUT',
      body: JSON.stringify(sectionData)
    });
  }

  async deleteSection(id) {
    return await this.request(`/sections/${id}`, {
      method: 'DELETE'
    });
  }

  async getSectionContent(sectionId, params = {}) {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.published_only) queryParams.append('published_only', 'true');
    if (params.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params.sort_order) queryParams.append('sort_order', params.sort_order);

    const queryString = queryParams.toString();
    const endpoint = `/sections/${sectionId}/content${queryString ? `?${queryString}` : ''}`;

    const response = await this.request(endpoint);
    
    // Process content items to ensure quality tags are properly handled
    if (response && response.success && Array.isArray(response.data)) {
      response.data = response.data.map(item => this.processContentItem(item));
    }
    
    return response;
  }

  async reorderSections(updateData) {
    return await this.request('/sections/reorder', {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }

  // Hero Carousel Management Methods
  async getCarouselContent() {
    console.log('[API Service] Getting carousel content from admin endpoint');
    const response = await this.request('/admin/content/carousel');
    
    console.log('[API Service] Carousel response:', response?.length || 0, 'items');
    
    // The admin endpoint returns array directly, not wrapped in response object
    if (Array.isArray(response)) {
      const processedItems = response.map(item => this.processContentItem(item));
      console.log('[API Service] Processed carousel items:', 
        processedItems.slice(0, 3).map(item => `${item.title} (position: ${item.carousel_position})`));
      return processedItems;
    }
    
    // Fallback for wrapped response format
    if (response && response.success && Array.isArray(response.data)) {
      response.data = response.data.map(item => this.processContentItem(item));
      return response.data;
    }
    
    return response || [];
  }

  async updateCarouselOrder(carouselData) {
    return await this.request('/admin/content/carousel/reorder', {
      method: 'PUT',
      body: JSON.stringify(carouselData)
    });
  }

  async addToCarousel(contentId, position = null) {
    return await this.request(`/admin/content/${contentId}`, {
      method: 'PUT',
      body: JSON.stringify({ add_to_carousel: 1, carousel_position: position })
    });
  }

  async removeFromCarousel(contentId) {
    return await this.request(`/admin/content/${contentId}`, {
      method: 'PUT',
      body: JSON.stringify({ add_to_carousel: 0, carousel_position: null })
    });
  }

  async updateCarouselCropSettings(contentId, cropSettings) {
    return await this.request(`/admin/content/${contentId}/crop-settings`, {
      method: 'PUT',
      body: JSON.stringify({ cropSettings })
    });
  }
}

// Create and export singleton instance
const apiService = new ApiService();

// Ensure methods are properly bound - FIXED 2025-07-06
apiService.getSectionContent = apiService.getSectionContent.bind(apiService);
apiService.reorderSections = apiService.reorderSections.bind(apiService);
apiService.getSections = apiService.getSections.bind(apiService);
apiService.getCategories = apiService.getCategories.bind(apiService);
apiService.createSection = apiService.createSection.bind(apiService);
apiService.updateSection = apiService.updateSection.bind(apiService);
apiService.deleteSection = apiService.deleteSection.bind(apiService);
apiService.bulkContentOperation = apiService.bulkContentOperation.bind(apiService);
apiService.getCarouselContent = apiService.getCarouselContent.bind(apiService);
apiService.updateCarouselOrder = apiService.updateCarouselOrder.bind(apiService);
apiService.addToCarousel = apiService.addToCarousel.bind(apiService);
apiService.removeFromCarousel = apiService.removeFromCarousel.bind(apiService);
apiService.updateCarouselCropSettings = apiService.updateCarouselCropSettings.bind(apiService);

export default apiService;

// Export individual methods for convenience
export const {
  login,
  logout,
  verifyToken,
  getContent,
  getContentById,
  createContent,
  updateContent,
  deleteContent,
  bulkContentOperation,
  getCategories,
  uploadImage,
  getDashboardStats,
  getHomepageContent,
  searchContent,
  getSections,
  getSectionById,
  getSectionContent,
  createSection,
  updateSection,
  deleteSection,
  getCarouselContent,
  updateCarouselOrder,
  addToCarousel,
  removeFromCarousel,
  updateCarouselCropSettings
} = apiService;
