# 🗄️ Hero Carousel Database Migration - Updated Steps

## ✅ **Validated Database Configuration**

From your `.env` file:
- **Database Name**: `stream_db`
- **Database User**: `stream_db_admin`
- **Database Host**: `localhost`
- **Database Port**: `3306`

---

## 📋 **Step-by-Step Migration Guide**

### **Step 1: Access Your Database**

**Option A: Using phpMyAdmin (Recommended)**
1. Go to your hosting control panel (cPanel/Plesk)
2. Open **phpMyAdmin**
3. **Select the `stream_db` database** from the left sidebar

**Option B: Using MySQL Command Line**
```bash
mysql -u stream_db_admin -p stream_db
# Enter password: Ohno@U2foundme
```

### **Step 2: Execute the Migration Script**

**For phpMyAdmin:**
1. Click the **"SQL"** tab
2. Copy and paste this entire script:

```sql
-- Hero Carousel Migration for stream_db database
USE stream_db;

-- Add carousel_position field to content table
ALTER TABLE content 
ADD COLUMN carousel_position INT DEFAULT NULL AFTER add_to_carousel;

-- Add crop_settings field to content table for poster positioning
ALTER TABLE content 
ADD COLUMN crop_settings JSON DEFAULT NULL AFTER carousel_position;

-- Add index for carousel_position for better performance
ALTER TABLE content 
ADD INDEX idx_carousel_position (carousel_position);

-- Update existing carousel items to have proper positions
SET @position = 0;
UPDATE content 
SET carousel_position = (@position := @position + 1) 
WHERE add_to_carousel = 1 
ORDER BY created_at ASC;

-- Verify the migration worked
SELECT 
    id, 
    title, 
    add_to_carousel, 
    carousel_position, 
    crop_settings,
    created_at 
FROM content 
WHERE add_to_carousel = 1 
ORDER BY carousel_position ASC;
```

3. Click **"Go"** or **"Execute"**

### **Step 3: Verify the Migration**

Run this verification script to confirm everything worked:

```sql
-- Verification Script for stream_db
USE stream_db;

-- Check if new columns were added
DESCRIBE content;

-- Check carousel items with new fields
SELECT 
    id, 
    title, 
    add_to_carousel, 
    carousel_position, 
    crop_settings,
    is_published,
    created_at 
FROM content 
WHERE add_to_carousel = 1 
ORDER BY carousel_position ASC;

-- Count total carousel items
SELECT COUNT(*) as total_carousel_items 
FROM content 
WHERE add_to_carousel = 1;

-- Check available content for carousel
SELECT 
    id, 
    title, 
    type,
    is_published,
    add_to_carousel
FROM content 
WHERE is_published = 1 AND add_to_carousel = 0
LIMIT 10;
```

### **Step 4: Expected Results**

✅ **You should see:**
- **DESCRIBE content** shows new columns: `carousel_position` and `crop_settings`
- **Carousel items** with position numbers (1, 2, 3, etc.)
- **Total count** of carousel items
- **Available content** that can be added to carousel

### **Step 5: Restart Your Application**

```bash
# SSH into your server and run:
pm2 restart streamdb-online

# Check if it's running properly:
pm2 status
pm2 logs streamdb-online --lines 20
```

### **Step 6: Test the Hero Carousel Manager**

1. Navigate to your **Admin Panel**
2. Click the **"Hero Carousel"** tab
3. You should now see your existing carousel content!

---

## 🚨 **Troubleshooting**

### **Common Issues & Solutions:**

**1. "Table 'stream_db.content' doesn't exist"**
- Make sure you selected the correct database (`stream_db`)
- Verify your database connection

**2. "Column 'carousel_position' already exists"**
- The migration was already run - skip to verification step
- Run only the verification script

**3. "Access denied for user 'stream_db_admin'"**
- Check your database credentials in `.env`
- Ensure the user has ALTER privileges

**4. "Unknown database 'stream_db'"**
- Verify the database name in your hosting control panel
- Check if the database exists

### **Need Help?**
- Share any error messages you encounter
- Run the verification script and share results
- Check PM2 logs: `pm2 logs streamdb-online`

---

## 🎯 **Ready to Proceed?**

1. **Execute the migration script** using the steps above
2. **Run the verification script** to confirm success
3. **Restart your PM2 process**
4. **Test the Hero Carousel tab** in your Admin Panel

Let me know when you've completed the migration and I'll help you verify everything is working correctly! 🚀