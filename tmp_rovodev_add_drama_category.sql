-- Add Drama category to the database
-- This script adds the Drama category to support the enhanced category system

USE stream_db;

-- Insert Drama category
INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
VALUES (
    'Drama', 
    'both', 
    'drama', 
    'Drama content including movies and series', 
    TRUE, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    is_active = VALUES(is_active),
    updated_at = NOW();

-- Verify the category was added
SELECT * FROM categories WHERE name = 'Drama';