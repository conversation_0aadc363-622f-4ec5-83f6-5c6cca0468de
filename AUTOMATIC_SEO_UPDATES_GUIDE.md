# 🔄 Automatic Daily SEO Updates for StreamDB

This guide explains how to set up automatic daily updates for your sitemap and SEO, ensuring Google and other search engines always have the latest information about your content.

## 📋 **What Gets Updated Automatically**

When the automatic update runs:

1. **Fresh Sitemap**: Generates a new sitemap with all current content
2. **Search Engine Notification**: Notifies Google and Bing about the update
3. **Logging**: Creates detailed logs of the update process
4. **Database Integration**: Pulls the latest content from your database

## 🛠️ **Setup Options**

You have several options for setting up automatic updates. Choose the one that works best for your hosting environment:

### **Option 1: Linux Cron Job (Recommended for VPS/Dedicated Servers)**

If your website is hosted on a Linux server with SSH access:

```bash
# 1. SSH into your server
ssh your-username@your-server

# 2. Edit crontab
crontab -e

# 3. Add this line to run daily at 3:00 AM (adjust path as needed)
0 3 * * * cd /path/to/your/website && /usr/bin/node scripts/auto-seo-update.js >> logs/cron-seo.log 2>&1
```

### **Option 2: Windows Task Scheduler (For Windows Servers)**

If your website is hosted on a Windows server:

1. Open Task Scheduler
2. Create a new Basic Task
3. Name it "StreamDB SEO Update"
4. Set trigger to Daily at 3:00 AM
5. Action: Start a Program
6. Program/script: `node.exe`
7. Arguments: `scripts/auto-seo-update.js`
8. Start in: `C:\path\to\your\website`

### **Option 3: Hosting Control Panel Cron Jobs**

If you're using cPanel, Plesk, or similar:

1. Log in to your hosting control panel
2. Find "Cron Jobs" or "Scheduled Tasks"
3. Create a new cron job
4. Command: `cd /home/<USER>/public_html && /usr/local/bin/node scripts/auto-seo-update.js`
5. Frequency: Daily at 3:00 AM

### **Option 4: GitHub Actions (For GitHub-hosted Projects)**

If your project is on GitHub:

1. Create a file at `.github/workflows/seo-update.yml`
2. Add this content:

```yaml
name: Daily SEO Update

on:
  schedule:
    - cron: '0 3 * * *'  # Runs at 3:00 AM UTC daily

jobs:
  update-seo:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm ci
      - run: npm run auto-seo-update
```

### **Option 5: Manual Execution with PM2 (For Node.js Servers)**

If you're using PM2 to manage your Node.js processes:

```bash
# Install PM2 if not already installed
npm install -g pm2

# Create a PM2 scheduled task
pm2 start scripts/auto-seo-update.js --name "seo-updater" --cron "0 3 * * *"

# Save the PM2 configuration
pm2 save

# Ensure PM2 starts on system boot
pm2 startup
```

## 🔍 **Monitoring the Updates**

### **Check the Logs**

The automatic update script creates detailed logs in the `logs/seo-updates.log` file:

```bash
# View the latest logs
tail -n 50 logs/seo-updates.log

# Monitor logs in real-time
tail -f logs/seo-updates.log
```

### **Verify in Google Search Console**

1. Log in to [Google Search Console](https://search.google.com/search-console)
2. Go to "Sitemaps"
3. Check the "Last read" date - it should update daily

## 🛠️ **Troubleshooting**

### **If Updates Aren't Running**

1. **Check Permissions**:
   ```bash
   # Make the script executable
   chmod +x scripts/auto-seo-update.js
   
   # Ensure Node.js can access your files
   chown -R your-user:your-group /path/to/your/website
   ```

2. **Check Node.js Path**:
   ```bash
   # Find the correct path to Node.js
   which node
   
   # Use the full path in your cron job
   0 3 * * * cd /path/to/your/website && /usr/bin/node scripts/auto-seo-update.js
   ```

3. **Test Manual Execution**:
   ```bash
   # Run the script manually to check for errors
   node scripts/auto-seo-update.js
   ```

### **If Search Engines Aren't Updating**

1. **Check Your Sitemap URL**:
   - Ensure `https://streamdb.online/sitemap.xml` is accessible
   - Verify it returns XML content with proper headers

2. **Force a Manual Update**:
   ```bash
   npm run auto-seo-update
   ```

3. **Check for Network Issues**:
   - Ensure your server can make outbound HTTPS requests
   - Check if your server is behind a firewall blocking outbound connections

## 📊 **Advanced Configuration**

You can customize the automatic update script by editing `scripts/auto-seo-update.js`:

### **Change Update Frequency**

Modify your cron schedule:
- `0 3 * * *` - Once daily at 3:00 AM
- `0 */12 * * *` - Every 12 hours
- `0 3 * * 1` - Once weekly (Mondays at 3:00 AM)

### **Add More Search Engines**

Edit the `searchEngines` array in the script to add more search engines:

```javascript
const searchEngines = [
  {
    name: 'Google',
    url: `https://www.google.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
  },
  {
    name: 'Bing',
    url: `https://www.bing.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
  },
  {
    name: 'Yandex',
    url: `https://webmaster.yandex.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
  }
];
```

### **Add Email Notifications**

To receive email notifications when updates complete or fail, you can add a simple email function using a service like Nodemailer.

## 🚀 **Best Practices**

1. **Run During Off-Peak Hours**: Schedule updates during low-traffic periods (like 3:00 AM)
2. **Keep Logs Rotated**: Set up log rotation to prevent log files from growing too large
3. **Monitor Disk Space**: Ensure your server has enough disk space for logs
4. **Regular Verification**: Check Google Search Console weekly to ensure updates are working
5. **Backup Before Changes**: Always backup your scripts before making changes

## 🎯 **Expected Results**

With automatic daily updates:

- ✅ Search engines always have your latest content
- ✅ New content gets indexed faster
- ✅ Better search rankings for fresh content
- ✅ No manual intervention required
- ✅ Consistent SEO performance

---

**Remember**: After setting up automatic updates, monitor the process for the first few days to ensure everything is working correctly. Check your logs and Google Search Console to verify the updates are happening as scheduled.
