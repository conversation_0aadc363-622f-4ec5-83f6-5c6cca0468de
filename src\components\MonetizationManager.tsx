import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * MonetizationManager Component
 * 
 * Handles Monetag ad integration for the website:
 * - PopUnder (OnClick) ads
 * - Push Notification ads
 * 
 * Features:
 * - Automatically excludes admin panel pages
 * - Applies to all current and future content pages
 * - Non-intrusive implementation
 * - Proper cleanup on route changes
 */
const MonetizationManager = () => {
  const location = useLocation();
  
  // Define admin routes where ads should NOT be shown
  const adminRoutes = [
    '/admin',
    '/login', 
    '/reset-password'
  ];
  
  // Check if current page is an admin page
  const isAdminPage = adminRoutes.some(route => 
    location.pathname.startsWith(route)
  );
  
  useEffect(() => {
    // Only load ads on non-admin pages
    if (!isAdminPage) {
      
      // Delay ad script loading to prevent blocking initial page render
      const loadAdsTimeout = setTimeout(() => {
        
        // 1. Load Push Notifications Script (Vigorous tag) with error handling
        if (!document.getElementById('monetag-push-script')) {
          const pushScript = document.createElement('script');
          pushScript.src = 'https://pertawee.net/act/files/tag.min.js?z=9595337';
          pushScript.setAttribute('data-cfasync', 'false');
          pushScript.async = true;
          pushScript.defer = true;
          pushScript.id = 'monetag-push-script';
          
          // Add error handling to prevent script failures from affecting page performance
          pushScript.onerror = () => {
            console.warn('Failed to load push notification ad script');
          };
          
          document.head.appendChild(pushScript);
        }
        
        // 2. Load PopUnder Script (OnClick Excited tag) with error handling
        if (!document.getElementById('monetag-popunder-script')) {
          const popunderScript = document.createElement('script');
          popunderScript.innerHTML = `
            try {
              (s=>{
                s.dataset.zone='9595332';
                s.src='https://al5sm.com/tag.min.js';
                s.onerror = () => console.warn('Failed to load popunder ad script');
              })([document.documentElement, document.body].filter(Boolean).pop().appendChild(document.createElement('script')));
            } catch(e) {
              console.warn('Error initializing popunder ad script:', e);
            }
          `;
          popunderScript.id = 'monetag-popunder-script';
          
          document.body.appendChild(popunderScript);
        }
        
      }, 2000); // 2 second delay to allow page to load first
      
      // Cleanup function
      return () => {
        clearTimeout(loadAdsTimeout);
        
        // Remove scripts when component unmounts or route changes to admin
        const existingPushScript = document.getElementById('monetag-push-script');
        const existingPopunderScript = document.getElementById('monetag-popunder-script');
        
        if (existingPushScript) {
          existingPushScript.remove();
        }
        
        if (existingPopunderScript) {
          existingPopunderScript.remove();
        }
      };
    }
  }, [isAdminPage, location.pathname]);
  
  // This component doesn't render any visible content
  return null;
};

export default MonetizationManager;
