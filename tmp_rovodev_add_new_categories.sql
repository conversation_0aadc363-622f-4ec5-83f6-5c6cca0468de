-- Add New Releases and Requested categories to the database
-- This script adds the two new categories to support the enhanced category system

-- Insert New Releases category
INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
VALUES (
    'New Releases', 
    'both', 
    'new-releases', 
    'Latest content added to the platform', 
    TRUE, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    is_active = VALUES(is_active),
    updated_at = NOW();

-- Insert Requested category
INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
VALUES (
    'Requested', 
    'both', 
    'requested', 
    'Content requested by users', 
    TRUE, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    is_active = VALUES(is_active),
    updated_at = NOW();

-- Verify the categories were added
SELECT * FROM categories WHERE name IN ('New Releases', 'Requested');