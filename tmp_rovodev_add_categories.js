const db = require('./server/config/database');
const fs = require('fs');

async function addNewCategories() {
  try {
    console.log('Adding new categories to database...');
    
    // Add New Releases category
    try {
      await db.execute(`
        INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
          description = VALUES(description),
          is_active = VALUES(is_active),
          updated_at = NOW()
      `, ['New Releases', 'both', 'new-releases', 'Latest content added to the platform', true]);
      console.log('✓ Added New Releases category');
    } catch (error) {
      console.log('New Releases category might already exist:', error.message);
    }
    
    // Add Requested category
    try {
      await db.execute(`
        INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
          description = VALUES(description),
          is_active = VALUES(is_active),
          updated_at = NOW()
      `, ['Requested', 'both', 'requested', 'Content requested by users', true]);
      console.log('✓ Added Requested category');
    } catch (error) {
      console.log('Requested category might already exist:', error.message);
    }
    
    // Verify the categories were added
    const categories = await db.execute('SELECT * FROM categories WHERE name IN (?, ?)', ['New Releases', 'Requested']);
    console.log('New categories in database:', categories);
    
    console.log('✓ Successfully completed category addition!');
    process.exit(0);
  } catch (error) {
    console.error('Error adding categories:', error);
    process.exit(1);
  }
}

addNewCategories();