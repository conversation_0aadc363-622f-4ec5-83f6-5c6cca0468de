# Complete Guide: Adding "Drama" Category to stream_db Database

## 🎯 GOAL
Add "Drama" category to your `stream_db` database's `categories` table.

## 📋 SQL SCRIPT TO RUN
```sql
-- Add Drama category to the database
USE stream_db;

-- Insert Drama category
INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
VALUES (
    'Drama', 
    'both', 
    'drama', 
    'Drama content including movies and series', 
    TRUE, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    is_active = VALUES(is_active),
    updated_at = NOW();

-- Verify the category was added
SELECT * FROM categories WHERE name = 'Drama';
```

---

## 🔧 METHOD 1: Using phpMyAdmin (Recommended)

### Step 1: Access phpMyAdmin
1. **Open your web browser**
2. **Navigate to your phpMyAdmin URL**:
   - If using cPanel: `https://yourdomain.com/cpanel` → Database section → phpMyAdmin
   - If using direct access: `https://yourdomain.com/phpmyadmin`
   - If using localhost: `http://localhost/phpmyadmin`

### Step 2: Login to phpMyAdmin
1. **Enter your database credentials**:
   - Username: (your database username)
   - Password: (your database password)
2. **Click "Go" or "Login"**

### Step 3: Select Your Database
1. **Look for "stream_db" in the left sidebar**
2. **Click on "stream_db"** to select it
3. **Verify you see the database tables** (including "categories" table)

### Step 4: Open SQL Tab
1. **Click the "SQL" tab** at the top of the page
2. **You'll see a large text area** for entering SQL commands

### Step 5: Execute the Script
1. **Copy the entire SQL script** from above
2. **Paste it into the SQL text area**
3. **Click the "Go" button** at the bottom right
4. **Wait for execution** - you should see success messages

### Step 6: Verify Results
1. **Look for success messages** like:
   - "1 row affected" or "Query executed successfully"
2. **Check the verification query results** - should show the new "Drama" category
3. **Navigate to the categories table**:
   - Click "categories" in left sidebar
   - Click "Browse" tab
   - Look for "Drama" entry

---

## 🔧 METHOD 2: Using MySQL Command Line

### Step 1: Access MySQL Command Line
**On Windows:**
```cmd
mysql -u your_username -p -h your_host
```

**On Linux/Mac:**
```bash
mysql -u your_username -p -h your_host
```

### Step 2: Enter Password
- **Type your database password** when prompted
- **Press Enter**

### Step 3: Select Database
```sql
USE stream_db;
```

### Step 4: Execute Script
**Copy and paste this command:**

```sql
INSERT INTO categories (name, type, slug, description, is_active, created_at, updated_at) 
VALUES (
    'Drama', 
    'both', 
    'drama', 
    'Drama content including movies and series', 
    TRUE, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    is_active = VALUES(is_active),
    updated_at = NOW();
```

### Step 5: Verify
```sql
SELECT * FROM categories WHERE name = 'Drama';
```

---

## 🔧 METHOD 3: Using Database Management Tool

### For MySQL Workbench:
1. **Open MySQL Workbench**
2. **Connect to your database server**
3. **Select "stream_db" schema**
4. **Open a new SQL tab** (Ctrl+T)
5. **Paste the script and execute** (Ctrl+Shift+Enter)

### For HeidiSQL:
1. **Open HeidiSQL**
2. **Connect to your database**
3. **Select "stream_db" database**
4. **Go to Query tab**
5. **Paste script and press F9**

### For Adminer:
1. **Access Adminer** (similar to phpMyAdmin)
2. **Login with database credentials**
3. **Select "stream_db" database**
4. **Click "SQL command"**
5. **Paste script and execute**

---

## 🔧 METHOD 4: Using cPanel File Manager + Script

### Step 1: Create SQL File
1. **Open cPanel File Manager**
2. **Navigate to your website directory**
3. **Create new file**: `add_drama_category.sql`
4. **Paste the SQL script** into the file
5. **Save the file**

### Step 2: Execute via Command Line
```bash
mysql -u your_username -p stream_db < add_drama_category.sql
```

---

## ✅ VERIFICATION STEPS

### After running the script, verify success:

1. **Check for Success Messages**:
   - "Query OK, 1 row affected" (for the INSERT)
   - No error messages

2. **Verify Categories Table**:
   ```sql
   SELECT id, name, slug, type, is_active FROM categories WHERE name = 'Drama';
   ```

3. **Expected Results**:
   - Should see "Drama" with slug "drama"
   - Should have `is_active = 1` and `type = 'both'`

4. **Check All Categories**:
   ```sql
   SELECT name, slug FROM categories ORDER BY name;
   ```
   - Should see "Drama" in the list alongside existing categories

5. **Test in Admin Panel**:
   - Go to your website's admin panel
   - Try adding new content
   - Check if "Drama" appears in category dropdown

---

## 🚨 TROUBLESHOOTING

### Common Issues & Solutions:

**Error: "Table 'categories' doesn't exist"**
- Solution: Make sure you're in the correct database (`USE stream_db;`)
- Check if your database name is different

**Error: "Access denied"**
- Solution: Verify your database username and password
- Ensure user has INSERT privileges on the database

**Error: "Duplicate entry"**
- Solution: "Drama" category already exists - this is fine!
- The script uses `ON DUPLICATE KEY UPDATE` to handle this

**No error but no results**
- Solution: Run the verification query to check if data was inserted
- Check if you're looking at the correct database

### Getting Database Connection Info:
Check your `.env` file in the project root:
```
DB_HOST=your_host
DB_USER=your_username  
DB_PASSWORD=your_password
DB_NAME=stream_db
```

---

## 🎯 QUICK VERIFICATION CHECKLIST

After running the script, you should be able to:

### ✅ Database Level:
- [ ] "Drama" category exists in categories table
- [ ] Category has correct slug "drama"
- [ ] Category is active (is_active = 1)
- [ ] Category type is "both"

### ✅ Application Level:
- [ ] Admin panel shows "Drama" in category dropdown
- [ ] `/categories` page displays "Drama" category
- [ ] `/category/drama` page loads without errors
- [ ] Can assign content to "Drama" category
- [ ] Content appears on Drama category page

---

## 📞 NEED HELP?

If you encounter issues:

1. **Check your hosting provider's documentation** for database access
2. **Contact your hosting support** for database connection help
3. **Verify database credentials** in your application's config files
4. **Take a screenshot of any error messages** for troubleshooting

---

## 🎉 SUCCESS CONFIRMATION

Once completed successfully:
- ✅ Database contains "Drama" category
- ✅ Admin panel shows "Drama" in dropdown
- ✅ Categories page shows "Drama" option
- ✅ `/category/drama` page works
- ✅ Content can be assigned to Drama category

The "Drama" category is now fully integrated into your streaming database system!