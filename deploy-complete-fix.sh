#!/bin/bash

echo "🚀 Starting Complete Episodes Fix Deployment..."
echo "================================================"

# Function to check if SSH connection works
check_ssh() {
    echo "🔍 Testing SSH connection..."
    if ssh -o ConnectTimeout=10 root@45.93.8.197 "echo 'SSH connection successful'" 2>/dev/null; then
        echo "✅ SSH connection working"
        return 0
    else
        echo "❌ SSH connection failed"
        return 1
    fi
}

# Function to deploy backend fixes
deploy_backend() {
    echo "📁 Deploying backend fixes..."
    
    # Create backup and deploy episodes.js
    ssh root@45.93.8.197 << 'EOF'
cd /var/www/streamdb_root/data/www/streamdb.online

# Create backup
echo "📋 Creating backup of current episodes.js..."
cp server/routes/episodes.js server/routes/episodes.js.backup.$(date +%Y%m%d_%H%M%S)

echo "✅ Backup created successfully"
EOF

    # Copy the fixed episodes.js file
    echo "📤 Copying fixed episodes.js to production..."
    scp server/routes/episodes.js root@45.93.8.197:/var/www/streamdb_root/data/www/streamdb.online/server/routes/
    
    if [ $? -eq 0 ]; then
        echo "✅ Backend files deployed successfully"
    else
        echo "❌ Backend deployment failed"
        return 1
    fi
}

# Function to deploy frontend fixes
deploy_frontend() {
    echo "📁 Deploying frontend fixes..."
    
    # Copy the fixed EpisodeManager.tsx file
    echo "📤 Copying fixed EpisodeManager.tsx to production..."
    scp src/components/admin/EpisodeManager.tsx root@45.93.8.197:/var/www/streamdb_root/data/www/streamdb.online/src/components/admin/
    
    if [ $? -eq 0 ]; then
        echo "✅ Frontend files deployed successfully"
    else
        echo "❌ Frontend deployment failed"
        return 1
    fi
}

# Function to restart services
restart_services() {
    echo "🔄 Restarting services on production server..."
    
    ssh root@45.93.8.197 << 'EOF'
cd /var/www/streamdb_root/data/www/streamdb.online

echo "🔄 Restarting backend server..."
pm2 restart index

echo "🏗️ Building frontend..."
npm run build

echo "✅ Services restarted and frontend rebuilt"
EOF

    if [ $? -eq 0 ]; then
        echo "✅ Services restarted successfully"
    else
        echo "❌ Service restart failed"
        return 1
    fi
}

# Function to verify deployment
verify_deployment() {
    echo "🔍 Verifying deployment..."
    
    ssh root@45.93.8.197 << 'EOF'
cd /var/www/streamdb_root/data/www/streamdb.online

echo "📊 Checking PM2 status..."
pm2 status

echo "📁 Checking if files exist..."
if [ -f "server/routes/episodes.js" ]; then
    echo "✅ episodes.js exists"
else
    echo "❌ episodes.js missing"
fi

if [ -f "src/components/admin/EpisodeManager.tsx" ]; then
    echo "✅ EpisodeManager.tsx exists"
else
    echo "❌ EpisodeManager.tsx missing"
fi

if [ -d "dist" ]; then
    echo "✅ Frontend build directory exists"
else
    echo "❌ Frontend build directory missing"
fi
EOF
}

# Main deployment process
main() {
    echo "🎯 Starting deployment process..."
    
    if check_ssh; then
        echo "✅ SSH connection verified"
        
        if deploy_backend; then
            echo "✅ Backend deployment completed"
            
            if deploy_frontend; then
                echo "✅ Frontend deployment completed"
                
                if restart_services; then
                    echo "✅ Services restarted"
                    
                    verify_deployment
                    
                    echo ""
                    echo "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
                    echo "================================================"
                    echo "✅ Backend database fixes applied"
                    echo "✅ Frontend error handling improved"
                    echo "✅ Services restarted"
                    echo "✅ Frontend rebuilt"
                    echo ""
                    echo "🔗 You can now test the All Web-Series tab at:"
                    echo "   https://streamdb.online/admin"
                    echo ""
                else
                    echo "❌ Service restart failed"
                    exit 1
                fi
            else
                echo "❌ Frontend deployment failed"
                exit 1
            fi
        else
            echo "❌ Backend deployment failed"
            exit 1
        fi
    else
        echo "❌ SSH connection failed. Please check your connection and try again."
        echo ""
        echo "📋 MANUAL DEPLOYMENT INSTRUCTIONS:"
        echo "=================================="
        echo "1. Copy server/routes/episodes.js to production server:"
        echo "   scp server/routes/episodes.js root@45.93.8.197:/var/www/streamdb_root/data/www/streamdb.online/server/routes/"
        echo ""
        echo "2. Copy src/components/admin/EpisodeManager.tsx to production server:"
        echo "   scp src/components/admin/EpisodeManager.tsx root@45.93.8.197:/var/www/streamdb_root/data/www/streamdb.online/src/components/admin/"
        echo ""
        echo "3. SSH into production server and restart services:"
        echo "   ssh root@45.93.8.197"
        echo "   cd /var/www/streamdb_root/data/www/streamdb.online"
        echo "   pm2 restart index"
        echo "   npm run build"
        echo ""
        exit 1
    fi
}

# Run main function
main
