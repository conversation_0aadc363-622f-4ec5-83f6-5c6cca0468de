#!/usr/bin/env node

/**
 * Quick Sitemap Fix for StreamDB
 * Generates a proper XML sitemap and optimized robots.txt
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 Fixing StreamDB sitemap and SEO files...\n');

const siteUrl = 'https://streamdb.online';
const currentDate = new Date().toISOString().split('T')[0];

// Static pages for sitemap
const staticPages = [
  {
    loc: siteUrl,
    changefreq: 'daily',
    priority: '1.0',
    comment: 'Homepage - Latest Movies & Series'
  },
  {
    loc: `${siteUrl}/movies`,
    changefreq: 'daily',
    priority: '0.9',
    comment: 'All Movies Collection'
  },
  {
    loc: `${siteUrl}/series`,
    changefreq: 'daily',
    priority: '0.9',
    comment: 'All TV Series Collection'
  },
  {
    loc: `${siteUrl}/requested`,
    changefreq: 'weekly',
    priority: '0.7',
    comment: 'User Requested Content'
  },
  {
    loc: `${siteUrl}/categories`,
    changefreq: 'weekly',
    priority: '0.8',
    comment: 'Content Categories'
  },
  {
    loc: `${siteUrl}/disclaimer`,
    changefreq: 'monthly',
    priority: '0.3',
    comment: 'Legal Disclaimer'
  },
  {
    loc: `${siteUrl}/dmca`,
    changefreq: 'monthly',
    priority: '0.3',
    comment: 'DMCA Policy'
  },
  {
    loc: `${siteUrl}/contact`,
    changefreq: 'monthly',
    priority: '0.5',
    comment: 'Contact Information'
  }
];

/**
 * Generate proper XML sitemap
 */
function generateSitemap() {
  console.log('🗺️  Generating XML sitemap...');
  
  let sitemapContent = '<?xml version="1.0" encoding="UTF-8"?>\n';
  sitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
  
  staticPages.forEach(page => {
    if (page.comment) {
      sitemapContent += `  <!-- ${page.comment} -->\n`;
    }
    sitemapContent += '  <url>\n';
    sitemapContent += `    <loc>${page.loc}</loc>\n`;
    sitemapContent += `    <lastmod>${currentDate}</lastmod>\n`;
    sitemapContent += `    <changefreq>${page.changefreq}</changefreq>\n`;
    sitemapContent += `    <priority>${page.priority}</priority>\n`;
    sitemapContent += '  </url>\n';
  });
  
  sitemapContent += '</urlset>';
  
  // Write to both public and dist directories
  const directories = [
    path.join(__dirname, '..', 'public'),
    path.join(__dirname, '..', 'dist')
  ];
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    const sitemapPath = path.join(dir, 'sitemap.xml');
    fs.writeFileSync(sitemapPath, sitemapContent);
    console.log(`✅ Sitemap written to ${sitemapPath}`);
  });
  
  console.log(`📄 Generated ${staticPages.length} URLs in sitemap`);
}

/**
 * Generate optimized robots.txt
 */
function generateRobotsTxt() {
  console.log('🤖 Generating optimized robots.txt...');
  
  const robotsContent = `# StreamDB.online - Robots.txt for Better SEO
# This file controls how search engines crawl and index our site
# Admin panel and sensitive areas are completely blocked from all crawlers

# Block ALL bots from admin panel and related paths
User-agent: *
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password
Disallow: /_admin
Disallow: /api/admin
Disallow: /dashboard
Disallow: /management

# Allow all other content for major search engines
User-agent: Googlebot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password

User-agent: Bingbot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password

User-agent: Twitterbot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*

User-agent: facebookexternalhit
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*

# Block aggressive crawlers and scrapers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

# Crawl delay for respectful bots
Crawl-delay: 1

# Sitemap location - CRITICAL for SEO
Sitemap: ${siteUrl}/sitemap.xml

# Allow important files for all crawlers
Allow: /favicon.ico
Allow: /favicon-*.png
Allow: /android-chrome-*.png
Allow: /apple-touch-icon.png
Allow: /site.webmanifest
Allow: /browserconfig.xml
Allow: /robots.txt
Allow: /sitemap.xml
`;

  // Write to both public and dist directories
  const directories = [
    path.join(__dirname, '..', 'public'),
    path.join(__dirname, '..', 'dist')
  ];
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    const robotsPath = path.join(dir, 'robots.txt');
    fs.writeFileSync(robotsPath, robotsContent);
    console.log(`✅ Robots.txt written to ${robotsPath}`);
  });
}

/**
 * Generate SEO meta tags template
 */
function generateMetaTags() {
  console.log('📝 Generating SEO meta tags...');
  
  const metaTemplate = `<!-- StreamDB SEO Meta Tags - Add to your HTML head section -->

<!-- Primary Meta Tags -->
<title>StreamDB - Free Movies & TV Series Online | Watch HD Content</title>
<meta name="title" content="StreamDB - Free Movies & TV Series Online | Watch HD Content">
<meta name="description" content="Watch free movies and TV series online in HD quality. StreamDB offers the latest movies, popular TV shows, and classic content. No registration required.">
<meta name="keywords" content="free movies, tv series, watch online, streaming, HD movies, latest movies, tv shows, entertainment">
<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
<meta name="language" content="English">
<meta name="author" content="StreamDB">
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="${siteUrl}/">
<meta property="og:title" content="StreamDB - Free Movies & TV Series Online">
<meta property="og:description" content="Watch free movies and TV series online in HD quality. Latest releases and classic content available.">
<meta property="og:image" content="${siteUrl}/og-image.jpg">
<meta property="og:site_name" content="StreamDB">
<meta property="og:locale" content="en_US">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="${siteUrl}/">
<meta property="twitter:title" content="StreamDB - Free Movies & TV Series Online">
<meta property="twitter:description" content="Watch free movies and TV series online in HD quality. Latest releases and classic content available.">
<meta property="twitter:image" content="${siteUrl}/twitter-image.jpg">

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="#000000">
<meta name="msapplication-TileColor" content="#000000">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- Canonical URL -->
<link rel="canonical" href="${siteUrl}/">

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "StreamDB",
  "url": "${siteUrl}",
  "description": "Free online streaming platform for movies and TV series",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "${siteUrl}/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  },
  "publisher": {
    "@type": "Organization",
    "name": "StreamDB",
    "url": "${siteUrl}"
  }
}
</script>`;

  const metaPath = path.join(__dirname, '..', 'SEO_META_TAGS.html');
  fs.writeFileSync(metaPath, metaTemplate);
  console.log(`✅ SEO meta tags template created at SEO_META_TAGS.html`);
}

/**
 * Main function
 */
function main() {
  try {
    console.log('🔧 Starting SEO fix process...\n');
    
    generateSitemap();
    generateRobotsTxt();
    generateMetaTags();
    
    console.log('\n🎉 SEO Fix Completed Successfully!');
    console.log('=' .repeat(50));
    console.log('✅ Fixed: Sitemap is now proper XML format');
    console.log('✅ Fixed: Server will serve XML correctly');
    console.log('✅ Enhanced: Robots.txt optimized for SEO');
    console.log('✅ Created: SEO meta tags template');
    console.log('\n🚀 Next Steps:');
    console.log('1. Deploy your updated server code');
    console.log('2. Add meta tags from SEO_META_TAGS.html to your HTML');
    console.log('3. Submit sitemap to Google Search Console');
    console.log(`4. Test: Visit ${siteUrl}/sitemap.xml (should show XML)`);
    console.log(`5. Test: Visit ${siteUrl}/robots.txt (should show updated content)`);
    console.log('\n🎯 Your "Sitemap is HTML" error should now be FIXED!');
    
  } catch (error) {
    console.error('❌ Error during SEO fix:', error.message);
    process.exit(1);
  }
}

// Run the fix
main();
