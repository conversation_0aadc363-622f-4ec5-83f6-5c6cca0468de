# 🚨 URGENT: Server Restart Loop Fix

## 🔍 **IMMEDIATE DIAGNOSIS STEPS:**

### **Step 1: Check PM2 Logs (CRITICAL)**
```bash
ssh root@45.93.8.197
pm2 logs streamdb-online --lines 50
# or
pm2 logs streamdb --lines 50
```

### **Step 2: Check for Missing Dependencies**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
npm install
```

### **Step 3: Test Server Manually**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
node index.js
# This will show the exact error
```

## 🛠️ **COMMON FIXES:**

### **Fix 1: Missing axios Dependency**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
npm install axios
```

### **Fix 2: Missing fetch (for OMDB)**
```bash
npm install node-fetch
# or
npm install undici
```

### **Fix 3: Revert to Working State**
If errors persist, temporarily disable new routes:

**Edit `/var/www/streamdb_onl_usr/data/www/streamdb.online/server/index.js`:**
```javascript
// Comment out these lines temporarily:
// app.use('/api/tmdb', require('./routes/tmdb'));
// app.use('/api/omdb', require('./routes/omdb'));
```

### **Fix 4: Check File Permissions**
```bash
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
chown -R www-data:www-data .
chmod -R 755 .
```

## 🚨 **EMERGENCY ROLLBACK:**

### **If Server Won't Start:**
1. **Backup current files:**
```bash
cp server/index.js server/index.js.backup
```

2. **Restore from backup:**
```bash
# Copy working index.js from backup if available
# Remove new route files temporarily
mv server/routes/tmdb.js server/routes/tmdb.js.disabled
mv server/routes/omdb.js server/routes/omdb.js.disabled
```

3. **Restart:**
```bash
pm2 restart streamdb-online
```

## 🔍 **LIKELY CAUSES:**

### **1. Missing Dependencies:**
- `axios` for TMDB service
- `node-fetch` or `undici` for OMDB service

### **2. Syntax Errors:**
- Check if files were corrupted during upload
- Verify file encoding (UTF-8)

### **3. Environment Variables:**
- Missing TMDB/OMDB API keys in production .env

### **4. File Paths:**
- Incorrect require() paths in new route files

## 📋 **QUICK DIAGNOSTIC COMMANDS:**

```bash
# Check PM2 status
pm2 status

# Check specific process logs
pm2 logs streamdb-online --lines 100

# Check if files exist
ls -la /var/www/streamdb_onl_usr/data/www/streamdb.online/server/routes/

# Check Node.js syntax
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
node -c index.js
node -c routes/tmdb.js
node -c routes/omdb.js

# Check dependencies
npm list axios
npm list node-fetch
```

## 🎯 **IMMEDIATE ACTION PLAN:**

1. **SSH into server**
2. **Check PM2 logs** (most important)
3. **Install missing dependencies**
4. **If still failing, disable new routes temporarily**
5. **Get server stable first, then debug**

**The logs will tell us exactly what's wrong!** 🔍