import { MediaItem } from './media';

/**
 * Extended MediaItem interface that supports both camelCase and snake_case field names
 * This helps bridge the gap between frontend and backend naming conventions
 */
export interface MediaItemExtended extends MediaItem {
  // Support both camelCase and snake_case versions of fields
  poster_url?: string;
  backdrop_url?: string;
  is_featured?: boolean;
  is_published?: boolean | number;
  add_to_carousel?: boolean | number;
  carousel_position?: number;
  crop_settings?: any;
  imdb_rating?: string;
  tmdb_id?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Helper function to normalize field names from snake_case to camelCase
 * @param item The media item with potentially mixed field naming
 * @returns A normalized media item with consistent camelCase fields
 */
export function normalizeMediaItem(item: MediaItemExtended): MediaItem {
  if (!item) return item;
  
  return {
    ...item,
    // Ensure camelCase fields are populated from snake_case if needed
    posterUrl: item.posterUrl || item.poster_url,
    backdropUrl: item.backdropUrl || item.backdrop_url,
    isFeatured: item.isFeatured !== undefined ? item.isFeatured : !!item.is_featured,
    isPublished: item.isPublished !== undefined ? item.isPublished : !!item.is_published,
    addToCarousel: item.addToCarousel !== undefined ? item.addToCarousel : !!item.add_to_carousel,
    carouselPosition: item.carouselPosition || item.carousel_position,
    cropSettings: item.cropSettings || item.crop_settings,
    imdbRating: item.imdbRating || item.imdb_rating,
    tmdbId: item.tmdbId || item.tmdb_id,
    createdAt: item.createdAt || item.created_at,
    updatedAt: item.updatedAt || item.updated_at
  };
}