# 🚨 MANUAL ROUTE ADDITION REQUIRED

## ⚠️ **CRITICAL ISSUE:**
PowerShell keeps corrupting the `index.js` file when trying to add routes automatically.

## ✅ **FILE RESTORED:**
- `server/index.js` has been restored from backup (12,141 bytes)
- File is now intact and functional

## 🔧 **MANUAL STEPS REQUIRED:**

### **Step 1: Open index.js in Text Editor**
Open: `G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\server\index.js`

### **Step 2: Find This Section (around line 225):**
```javascript
app.use('/api/sections', require('./routes/sections'));
app.use('/api/episodes', require('./routes/episodes'));
// app.use('/api/database', require('./routes/database-diagnostic'));
```

### **Step 3: Add These Two Lines:**
**Add AFTER the episodes line:**
```javascript
app.use('/api/sections', require('./routes/sections'));
app.use('/api/episodes', require('./routes/episodes'));
app.use('/api/tmdb', require('./routes/tmdb'));
app.use('/api/omdb', require('./routes/omdb'));
// app.use('/api/database', require('./routes/database-diagnostic'));
```

### **Step 4: Save the File**
- Save and verify file size remains around 12,000+ bytes
- Do NOT use PowerShell to edit this file

## 📋 **DEPLOYMENT CHECKLIST:**

### **Files Ready for Ubuntu Server:**
1. ✅ `server/index.js` - **RESTORED** (needs manual route addition)
2. ✅ `server/routes/tmdb.js` - NEW backend TMDB route
3. ✅ `server/routes/omdb.js` - NEW backend OMDB route  
4. ✅ `server/.env` - Database config corrected for Ubuntu
5. ✅ `src/components/admin/AddTitleForm.tsx` - Backend API integration
6. ✅ `src/components/admin/TMDBSearchDialog.tsx` - Backend API integration
7. ✅ `src/services/omdbService.ts` - Enhanced data extraction

### **After Manual Route Addition:**
- Upload all files to: `/var/www/streamdb_onl_usr/data/www/streamdb.online/`
- SSH to server: `ssh root@45.93.8.197`
- Restart PM2: `pm2 restart streamdb`
- Test: `https://streamdb.online/admin`

## 🎯 **EXPECTED RESULTS:**
- ✅ 502 Error resolved
- ✅ TMDB API: Brooklyn Nine-Nine (ID: 48891) works correctly
- ✅ OMDB API: Complete form field population
- ✅ Admin panel accessible

**Please manually add the two route lines to avoid file corruption!**