const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();
const db = require('../config/database');

// Helper function to generate unique IDs
function generateId(prefix) {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${randomString}`;
}

// Helper function to get next season number
async function getNextSeasonNumber(contentId) {
  try {
    const result = await db.execute(
      'SELECT MAX(season_number) as max_season FROM seasons WHERE content_id = ?',
      [contentId]
    );
    
    // Handle both MySQL2 result formats: [rows] or [rows, fields]
    let rows;
    if (Array.isArray(result)) {
      if (result.length > 0 && Array.isArray(result[0])) {
        rows = result[0];
      } else {
        rows = result;
      }
    } else {
      rows = [];
    }
    return (rows[0]?.max_season || 0) + 1;
  } catch (error) {
    console.error('Error getting next season number:', error);
    return 1;
  }
}

// Helper function to get next episode number
async function getNextEpisodeNumber(seasonId) {
  try {
    const result = await db.execute(
      'SELECT MAX(episode_number) as max_episode FROM episodes WHERE season_id = ?',
      [seasonId]
    );
    
    // Handle both MySQL2 result formats: [rows] or [rows, fields]
    let rows;
    if (Array.isArray(result)) {
      if (result.length > 0 && Array.isArray(result[0])) {
        rows = result[0];
      } else {
        rows = result;
      }
    } else {
      rows = [];
    }
    return (rows[0]?.max_episode || 0) + 1;
  } catch (error) {
    console.error('Error getting next episode number:', error);
    return 1;
  }
}

// GET all episodes for a specific content
router.get('/content/:contentId', async (req, res) => {
  try {
    const { contentId } = req.params;
    
    const episodes = await db.execute(`
      SELECT e.*, s.season_number, s.title as season_title
      FROM episodes e
      JOIN seasons s ON e.season_id = s.id
      WHERE e.content_id = ?
      ORDER BY s.season_number, e.episode_number
    `, [contentId]);

    res.json({
      success: true,
      data: episodes
    });
  } catch (error) {
    console.error('Error fetching episodes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episodes',
      error: error.message
    });
  }
});

// GET episodes for a specific season
router.get('/season/:seasonId', async (req, res) => {
  try {
    const { seasonId } = req.params;
    
    const episodes = await db.execute(
      'SELECT * FROM episodes WHERE season_id = ? ORDER BY episode_number',
      [seasonId]
    );

    res.json({
      success: true,
      data: episodes
    });
  } catch (error) {
    console.error('Error fetching season episodes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch season episodes',
      error: error.message
    });
  }
});

// GET single episode
router.get('/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;
    
    const episode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    if (!episode || episode.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    res.json({
      success: true,
      data: episode[0]
    });
  } catch (error) {
    console.error('Error fetching episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episode',
      error: error.message
    });
  }
});

// POST create new season
router.post('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    // Validate required fields
    if (!title || title.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Season title is required'
      });
    }

    // Generate season number if not provided
    let finalSeasonNumber = seasonNumber;
    if (!finalSeasonNumber) {
      finalSeasonNumber = await getNextSeasonNumber(contentId);
    }

    // Generate unique season ID
    const seasonId = generateId('season');

    // Insert season into database
    await db.execute(
      `INSERT INTO seasons (
        id, content_id, season_number, title, description, poster_url, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        seasonId,
        contentId,
        finalSeasonNumber,
        title,
        description || null,
        posterUrl || null
      ]
    );

    // Update parent content
    await db.execute(
      'UPDATE content SET updated_at = NOW() WHERE id = ?',
      [contentId]
    );

    // Get the created season
    const newSeason = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );

    res.status(201).json({
      success: true,
      message: 'Season created successfully',
      data: newSeason[0] || { id: seasonId, title, seasonNumber: finalSeasonNumber }
    });

  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create season',
      error: error.message
    });
  }
});

// POST create new episode - BULLETPROOF VERSION
router.post('/content/:contentId/seasons/:seasonId/episodes', async (req, res) => {
  console.log('=== BULLETPROOF EPISODE CREATION START ===');
  console.log('Timestamp:', new Date().toISOString());
  console.log('Request params:', req.params);
  console.log('Raw request body:', req.body);
  console.log('Request body JSON:', JSON.stringify(req.body, null, 2));
  
  try {
    // Test database connection first
    try {
      const dbTest = await db.execute('SELECT 1 as test, NOW() as timestamp');
      console.log('Database connection successful:', dbTest);
    } catch (dbError) {
      console.error('Database connection failed:', dbError);
      return res.status(500).json({
        success: false,
        message: 'Database connection failed',
        error: dbError.message,
        code: dbError.code,
        step: 'database_connection'
      });
    }

    const { contentId, seasonId } = req.params;
    let { title, secureVideoLinks, episodeNumber, description, runtime, airDate, thumbnailUrl } = req.body;
    
    console.log('Extracted params:', { contentId, seasonId });
    console.log('Extracted body data:', { 
      title, 
      secureVideoLinks: secureVideoLinks ? 'present' : 'missing',
      episodeNumber,
      description: description ? 'present' : 'missing'
    });

    // Clean up undefined values
    if (description === undefined || description === 'undefined') description = null;
    if (runtime === undefined || runtime === 'undefined') runtime = null;
    if (airDate === undefined || airDate === 'undefined') airDate = null;
    if (thumbnailUrl === undefined || thumbnailUrl === 'undefined') thumbnailUrl = null;

    // Validate required fields manually
    if (!title || title.trim() === '') {
      console.log('Validation failed: Missing title');
      return res.status(400).json({
        success: false,
        message: 'Episode title is required',
        receivedTitle: title
      });
    }

    if (!secureVideoLinks || secureVideoLinks.trim() === '') {
      console.log('Validation failed: Missing video links');
      return res.status(400).json({
        success: false,
        message: 'Episode video embed links are required',
        receivedVideoLinks: secureVideoLinks
      });
    }

    console.log('Basic validation passed');

    // Check if season exists
    console.log('Checking if season exists...');
    try {
      const seasonCheck = await db.execute(
        'SELECT id, season_number, title FROM seasons WHERE id = ? AND content_id = ?',
        [seasonId, contentId]
      );

      const seasonRows = Array.isArray(seasonCheck) ? seasonCheck : (seasonCheck[0] || []);
      console.log('Season check result:', seasonRows);

      if (!seasonRows || seasonRows.length === 0) {
        console.log('Season not found');
        return res.status(404).json({
          success: false,
          message: 'Season not found or does not belong to this content',
          seasonId,
          contentId
        });
      }
      console.log('Season exists:', seasonRows[0]);
    } catch (seasonError) {
      console.error('Season check failed:', seasonError);
      return res.status(500).json({
        success: false,
        message: 'Season check failed',
        error: seasonError.message,
        step: 'season_check'
      });
    }

    // Generate episode number if not provided
    let finalEpisodeNumber = episodeNumber;
    if (!finalEpisodeNumber) {
      console.log('Generating episode number...');
      try {
        finalEpisodeNumber = await getNextEpisodeNumber(seasonId);
        console.log('Generated episode number:', finalEpisodeNumber);
      } catch (episodeNumError) {
        console.error('Episode number generation failed:', episodeNumError);
        finalEpisodeNumber = 1;
        console.log('Using fallback episode number:', finalEpisodeNumber);
      }
    }

    // Generate unique episode ID
    const episodeId = generateId('episode');
    console.log('Generated episode ID:', episodeId);

    // Prepare insert data
    const insertData = [
      episodeId,
      seasonId,
      contentId,
      finalEpisodeNumber,
      title.trim(),
      description,
      secureVideoLinks.trim(),
      runtime,
      airDate,
      thumbnailUrl
    ];

    console.log('Insert data prepared:', insertData);

    // Insert episode into database
    console.log('Inserting episode into database...');
    try {
      const insertResult = await db.execute(
        `INSERT INTO episodes (
          id, season_id, content_id, episode_number, title, description,
          secure_video_links, runtime, air_date, thumbnail_url, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        insertData
      );
      console.log('Episode inserted successfully:', insertResult);
    } catch (insertError) {
      console.error('Episode insert failed:', insertError);
      console.error('SQL Error Code:', insertError.code);
      console.error('SQL Error Message:', insertError.sqlMessage);
      console.error('SQL State:', insertError.sqlState);
      
      return res.status(500).json({
        success: false,
        message: 'Episode insert failed',
        error: insertError.message,
        sqlError: insertError.sqlMessage,
        sqlCode: insertError.code,
        sqlState: insertError.sqlState,
        step: 'database_insert',
        insertData: insertData
      });
    }

    // Update parent content timestamp
    console.log('Updating parent content timestamp...');
    try {
      await db.execute(
        'UPDATE content SET updated_at = NOW() WHERE id = ?',
        [contentId]
      );
      console.log('Parent content updated');
    } catch (updateError) {
      console.error('Parent content update failed:', updateError);
      // Don't fail the request for this
    }

    // Return success response with created episode data
    const responseData = {
      id: episodeId,
      season_id: seasonId,
      content_id: contentId,
      episode_number: finalEpisodeNumber,
      title: title.trim(),
      description,
      secure_video_links: secureVideoLinks.trim(),
      runtime,
      air_date: airDate,
      thumbnail_url: thumbnailUrl
    };

    console.log('SUCCESS - Episode created successfully');
    console.log('Response data:', responseData);

    res.status(201).json({
      success: true,
      message: 'Episode created successfully',
      data: responseData
    });

  } catch (error) {
    console.error('=== CRITICAL EPISODE CREATION ERROR ===');
    console.error('Error type:', typeof error);
    console.error('Error name:', error.name);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    console.error('Error code:', error.code);
    console.error('Error sqlMessage:', error.sqlMessage);
    console.error('Error sqlState:', error.sqlState);
    console.error('Full error object:', error);
    
    res.status(500).json({
      success: false,
      message: 'Critical episode creation error',
      error: error.message,
      errorType: error.name,
      errorCode: error.code,
      sqlError: error.sqlMessage,
      sqlState: error.sqlState,
      step: 'critical_error',
      timestamp: new Date().toISOString()
    });
  }
  
  console.log('=== BULLETPROOF EPISODE CREATION END ===');
});

// PUT update episode
router.put('/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;
    const { title, description, secureVideoLinks, runtime, airDate, thumbnailUrl } = req.body;

    // Check if episode exists
    const existingEpisode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    if (!existingEpisode || existingEpisode.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    // Update episode
    await db.execute(
      `UPDATE episodes SET 
        title = ?, description = ?, secure_video_links = ?, 
        runtime = ?, air_date = ?, thumbnail_url = ?, updated_at = NOW()
      WHERE id = ?`,
      [title, description, secureVideoLinks, runtime, airDate, thumbnailUrl, episodeId]
    );

    // Get updated episode
    const updatedEpisode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    res.json({
      success: true,
      message: 'Episode updated successfully',
      data: updatedEpisode[0]
    });

  } catch (error) {
    console.error('Error updating episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update episode',
      error: error.message
    });
  }
});

// DELETE episode
router.delete('/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;

    // Check if episode exists
    const existingEpisode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    if (!existingEpisode || existingEpisode.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    // Delete episode
    await db.execute('DELETE FROM episodes WHERE id = ?', [episodeId]);

    res.json({
      success: true,
      message: 'Episode deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete episode',
      error: error.message
    });
  }
});

module.exports = router;