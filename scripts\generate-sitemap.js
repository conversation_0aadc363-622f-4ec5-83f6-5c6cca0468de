#!/usr/bin/env node

/**
 * Sitemap Generator for StreamDB
 * Automatically generates sitemap.xml with current content and excludes admin routes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🗺️  Generating sitemap.xml for StreamDB...');

// Base configuration
const siteUrl = 'https://streamdb.online';
const currentDate = new Date().toISOString().split('T')[0];

// Static pages with their priorities and change frequencies
const staticPages = [
  {
    loc: siteUrl,
    changefreq: 'daily',
    priority: '1.0',
    comment: 'Homepage'
  },
  {
    loc: `${siteUrl}/movies`,
    changefreq: 'daily',
    priority: '0.9',
    comment: 'All Movies'
  },
  {
    loc: `${siteUrl}/series`,
    changefreq: 'daily',
    priority: '0.9',
    comment: 'All Series'
  },
  {
    loc: `${siteUrl}/requested`,
    changefreq: 'weekly',
    priority: '0.7',
    comment: 'Requested Content'
  },
  {
    loc: `${siteUrl}/categories`,
    changefreq: 'weekly',
    priority: '0.8',
    comment: 'Categories'
  },
  {
    loc: `${siteUrl}/disclaimer`,
    changefreq: 'monthly',
    priority: '0.3',
    comment: 'Legal - Disclaimer'
  },
  {
    loc: `${siteUrl}/dmca`,
    changefreq: 'monthly',
    priority: '0.3',
    comment: 'Legal - DMCA'
  },
  {
    loc: `${siteUrl}/contact`,
    changefreq: 'monthly',
    priority: '0.5',
    comment: 'Contact Page'
  }
];

// Function to fetch dynamic content from database and API
async function getDynamicContent() {
  const dynamicPages = [];

  try {
    // Try to connect to database and fetch content
    const mysql = await import('mysql2/promise');
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'streaming_db',
      charset: 'utf8mb4'
    };

    console.log('🔗 Connecting to database for dynamic content...');
    const connection = await mysql.createConnection(dbConfig);

    // Fetch published content (movies and series)
    const [content] = await connection.execute(`
      SELECT id, title, type, updated_at
      FROM content
      WHERE is_published = 1
      ORDER BY updated_at DESC
    `);

    content.forEach(item => {
      dynamicPages.push({
        loc: `${siteUrl}/content/${item.id}`,
        changefreq: 'weekly',
        priority: '0.6',
        comment: `${item.type === 'movie' ? 'Movie' : 'Series'}: ${item.title}`,
        lastmod: item.updated_at ? new Date(item.updated_at).toISOString().split('T')[0] : currentDate
      });
    });

    // Fetch active categories
    const [categories] = await connection.execute(`
      SELECT id, name, slug, updated_at
      FROM categories
      WHERE is_active = 1
      ORDER BY name
    `);

    categories.forEach(category => {
      dynamicPages.push({
        loc: `${siteUrl}/category/${category.slug || category.id}`,
        changefreq: 'weekly',
        priority: '0.7',
        comment: `Category: ${category.name}`,
        lastmod: category.updated_at ? new Date(category.updated_at).toISOString().split('T')[0] : currentDate
      });
    });

    await connection.end();
    console.log(`✅ Fetched ${content.length} content items and ${categories.length} categories from database`);

  } catch (error) {
    console.warn(`⚠️  Could not fetch dynamic content from database: ${error.message}`);
    console.log('📝 Continuing with static pages only...');
  }

  return dynamicPages;
}

// Generate sitemap XML content
async function generateSitemap() {
  try {
    const dynamicPages = await getDynamicContent();
    const allPages = [...staticPages, ...dynamicPages];
    
    let sitemapContent = '<?xml version="1.0" encoding="UTF-8"?>\n';
    sitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    
    // Add each URL to the sitemap
    allPages.forEach(page => {
      if (page.comment) {
        sitemapContent += `  <!-- ${page.comment} -->\n`;
      }
      sitemapContent += '  <url>\n';
      sitemapContent += `    <loc>${page.loc}</loc>\n`;
      sitemapContent += `    <lastmod>${page.lastmod || currentDate}</lastmod>\n`;
      sitemapContent += `    <changefreq>${page.changefreq}</changefreq>\n`;
      sitemapContent += `    <priority>${page.priority}</priority>\n`;
      sitemapContent += '  </url>\n';
    });
    
    sitemapContent += '</urlset>';
    
    // Write sitemap to public directory
    const publicDir = path.join(__dirname, '..', 'public');
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    
    fs.writeFileSync(sitemapPath, sitemapContent);
    console.log(`✅ Sitemap generated successfully at ${sitemapPath}`);
    console.log(`📄 Generated ${allPages.length} URLs`);
    
    // Also copy to dist directory if it exists (for production builds)
    const distDir = path.join(__dirname, '..', 'dist');
    if (fs.existsSync(distDir)) {
      const distSitemapPath = path.join(distDir, 'sitemap.xml');
      fs.writeFileSync(distSitemapPath, sitemapContent);
      console.log(`✅ Sitemap also copied to ${distSitemapPath}`);
    }
    
    return sitemapPath;
  } catch (error) {
    console.error(`❌ Error generating sitemap: ${error.message}`);
    process.exit(1);
  }
}

// Run the generator
if (import.meta.url === `file://${process.argv[1]}`) {
  generateSitemap();
}

export { generateSitemap };