#!/usr/bin/env node

/**
 * SEO Verification Script for StreamDB
 * Verifies that SEO implementation is working correctly
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Verifying SEO implementation for StreamDB...\n');

const siteUrl = 'https://streamdb.online';
const publicDir = path.join(__dirname, '..', 'public');

// Test results
const results = {
  robotsTxt: { status: '❌', message: '' },
  sitemapXml: { status: '❌', message: '' },
  adminBlocking: { status: '❌', message: '' },
  sitemapContent: { status: '❌', message: '' }
};

/**
 * Check if robots.txt exists and has admin blocking
 */
function checkRobotsTxt() {
  try {
    const robotsPath = path.join(publicDir, 'robots.txt');
    
    if (!fs.existsSync(robotsPath)) {
      results.robotsTxt = { status: '❌', message: 'robots.txt file not found' };
      return;
    }

    const robotsContent = fs.readFileSync(robotsPath, 'utf8');
    
    // Check for admin blocking
    const hasAdminBlocking = robotsContent.includes('Disallow: /admin');
    const hasSitemapReference = robotsContent.includes('Sitemap: https://streamdb.online/sitemap.xml');
    
    if (hasAdminBlocking && hasSitemapReference) {
      results.robotsTxt = { status: '✅', message: 'robots.txt properly configured with admin blocking and sitemap reference' };
    } else if (hasAdminBlocking) {
      results.robotsTxt = { status: '⚠️', message: 'robots.txt has admin blocking but missing sitemap reference' };
    } else {
      results.robotsTxt = { status: '❌', message: 'robots.txt missing admin blocking rules' };
    }
  } catch (error) {
    results.robotsTxt = { status: '❌', message: `Error reading robots.txt: ${error.message}` };
  }
}

/**
 * Check if sitemap.xml exists and is valid
 */
function checkSitemapXml() {
  try {
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    
    if (!fs.existsSync(sitemapPath)) {
      results.sitemapXml = { status: '❌', message: 'sitemap.xml file not found' };
      return;
    }

    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    
    // Check if it's valid XML
    const isValidXml = sitemapContent.includes('<?xml version="1.0"') && 
                      sitemapContent.includes('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">');
    
    if (isValidXml) {
      results.sitemapXml = { status: '✅', message: 'sitemap.xml exists and appears to be valid XML' };
    } else {
      results.sitemapXml = { status: '❌', message: 'sitemap.xml exists but is not valid XML format' };
    }
  } catch (error) {
    results.sitemapXml = { status: '❌', message: `Error reading sitemap.xml: ${error.message}` };
  }
}

/**
 * Check that admin routes are not in sitemap
 */
function checkAdminBlocking() {
  try {
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    
    if (!fs.existsSync(sitemapPath)) {
      results.adminBlocking = { status: '❌', message: 'Cannot check admin blocking - sitemap.xml not found' };
      return;
    }

    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    
    // Check for admin routes in sitemap (should NOT be present)
    const adminRoutes = ['/admin', '/login', '/reset-password'];
    const foundAdminRoutes = adminRoutes.filter(route => sitemapContent.includes(route));
    
    if (foundAdminRoutes.length === 0) {
      results.adminBlocking = { status: '✅', message: 'Admin routes properly excluded from sitemap' };
    } else {
      results.adminBlocking = { status: '❌', message: `Admin routes found in sitemap: ${foundAdminRoutes.join(', ')}` };
    }
  } catch (error) {
    results.adminBlocking = { status: '❌', message: `Error checking admin blocking: ${error.message}` };
  }
}

/**
 * Check sitemap content quality
 */
function checkSitemapContent() {
  try {
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    
    if (!fs.existsSync(sitemapPath)) {
      results.sitemapContent = { status: '❌', message: 'Cannot check sitemap content - file not found' };
      return;
    }

    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    
    // Count URLs
    const urlMatches = sitemapContent.match(/<url>/g);
    const urlCount = urlMatches ? urlMatches.length : 0;
    
    // Check for required pages
    const requiredPages = [
      'https://streamdb.online',
      'https://streamdb.online/movies',
      'https://streamdb.online/series',
      'https://streamdb.online/categories'
    ];
    
    const missingPages = requiredPages.filter(page => !sitemapContent.includes(page));
    
    if (urlCount > 0 && missingPages.length === 0) {
      results.sitemapContent = { status: '✅', message: `Sitemap contains ${urlCount} URLs with all required pages` };
    } else if (urlCount > 0) {
      results.sitemapContent = { status: '⚠️', message: `Sitemap has ${urlCount} URLs but missing: ${missingPages.join(', ')}` };
    } else {
      results.sitemapContent = { status: '❌', message: 'Sitemap appears to be empty or malformed' };
    }
  } catch (error) {
    results.sitemapContent = { status: '❌', message: `Error checking sitemap content: ${error.message}` };
  }
}

/**
 * Test live website URLs
 */
function testLiveUrls() {
  return new Promise((resolve) => {
    console.log('🌐 Testing live website URLs...\n');
    
    const urlsToTest = [
      { url: `${siteUrl}/robots.txt`, name: 'Robots.txt' },
      { url: `${siteUrl}/sitemap.xml`, name: 'Sitemap.xml' }
    ];
    
    let completed = 0;
    const liveResults = {};
    
    urlsToTest.forEach(({ url, name }) => {
      const urlObj = new URL(url);
      
      const options = {
        hostname: urlObj.hostname,
        port: 443,
        path: urlObj.pathname,
        method: 'HEAD',
        timeout: 5000
      };

      const req = https.request(options, (res) => {
        liveResults[name] = {
          status: res.statusCode >= 200 && res.statusCode < 300 ? '✅' : '❌',
          message: `HTTP ${res.statusCode}`
        };
        
        completed++;
        if (completed === urlsToTest.length) {
          resolve(liveResults);
        }
      });

      req.on('error', (error) => {
        liveResults[name] = {
          status: '❌',
          message: `Error: ${error.message}`
        };
        
        completed++;
        if (completed === urlsToTest.length) {
          resolve(liveResults);
        }
      });

      req.on('timeout', () => {
        liveResults[name] = {
          status: '❌',
          message: 'Request timeout'
        };
        req.destroy();
        
        completed++;
        if (completed === urlsToTest.length) {
          resolve(liveResults);
        }
      });

      req.end();
    });
  });
}

/**
 * Main verification function
 */
async function runVerification() {
  console.log('📁 Checking local files...\n');
  
  checkRobotsTxt();
  checkSitemapXml();
  checkAdminBlocking();
  checkSitemapContent();
  
  // Print local results
  console.log('Local File Verification:');
  console.log('========================');
  Object.entries(results).forEach(([test, result]) => {
    console.log(`${result.status} ${test}: ${result.message}`);
  });
  
  console.log('\n');
  
  // Test live URLs
  try {
    const liveResults = await testLiveUrls();
    console.log('Live URL Verification:');
    console.log('======================');
    Object.entries(liveResults).forEach(([test, result]) => {
      console.log(`${result.status} ${test}: ${result.message}`);
    });
  } catch (error) {
    console.log('❌ Live URL testing failed:', error.message);
  }
  
  // Summary
  console.log('\n📊 Summary:');
  console.log('===========');
  
  const localPassed = Object.values(results).filter(r => r.status === '✅').length;
  const localTotal = Object.keys(results).length;
  
  console.log(`Local tests: ${localPassed}/${localTotal} passed`);
  
  if (localPassed === localTotal) {
    console.log('🎉 All local SEO checks passed! Your SEO implementation is ready.');
  } else {
    console.log('⚠️  Some SEO checks failed. Please review the issues above.');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('===============');
  console.log('1. Deploy your changes to production');
  console.log('2. Run: npm run ping-search-engines');
  console.log('3. Submit sitemap to Google Search Console');
  console.log('4. Submit sitemap to Bing Webmaster Tools');
  console.log('5. Monitor indexing status');
}

// Run verification
runVerification().catch(console.error);