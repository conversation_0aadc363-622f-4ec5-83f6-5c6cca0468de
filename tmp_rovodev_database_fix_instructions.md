# Database Fix Instructions for Quality Label Feature

## Issue
The content table doesn't have a `quality_label` column, but our code is trying to use it.

## Solution Options

### Option 1: Add the missing column (RECOMMENDED)
Run this SQL command in your database:

```sql
ALTER TABLE content ADD COLUMN quality_label VARCHAR(100) DEFAULT NULL AFTER quality;
```

### Option 2: Use existing quality column structure
Modify the backend to store quality labels in the existing `quality` field with a special format.

## Steps to Fix

### For Production Database:
1. Connect to your MySQL database
2. Run: `USE streamingdb;` (or your database name)
3. Run: `ALTER TABLE content ADD COLUMN quality_label VARCHAR(100) DEFAULT NULL AFTER quality;`
4. Verify: `DESCRIBE content;` should show the new quality_label column

### Alternative: Quick Fix (No Database Changes)
If you can't modify the database right now, I can modify the code to use the existing `quality` field.