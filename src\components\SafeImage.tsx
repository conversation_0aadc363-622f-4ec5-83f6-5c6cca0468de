import React, { useState, useRef, useEffect, useCallback } from 'react';

interface SafeImageProps {
  src?: string;
  alt?: string;
  fallbackSrc?: string;
  placeholder?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
  [key: string]: any;
}

// Image cache to prevent redundant network requests
const imageCache = new Map<string, { status: 'loading' | 'loaded' | 'error', callbacks: Array<() => void> }>();

const SafeImage: React.FC<SafeImageProps> = ({
  src,
  alt = 'Image',
  fallbackSrc = '/placeholder-image.jpg',
  placeholder,
  className = '',
  style,
  onLoad,
  onError,
  ...props
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const imgRef = useRef<HTMLImageElement>(null);
  const mountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const loadImage = useCallback((imageUrl: string, isFallback = false) => {
    if (!imageUrl || !mountedRef.current) return;

    // Check cache first
    const cached = imageCache.get(imageUrl);
    if (cached) {
      if (cached.status === 'loaded') {
        if (mountedRef.current) {
          setImageSrc(imageUrl);
          setIsLoading(false);
          setHasError(false);
          onLoad?.();
        }
        return;
      } else if (cached.status === 'error') {
        if (!isFallback && fallbackSrc && fallbackSrc !== imageUrl) {
          loadImage(fallbackSrc, true);
        } else if (mountedRef.current) {
          setHasError(true);
          setIsLoading(false);
          onError?.();
        }
        return;
      } else if (cached.status === 'loading') {
        // Add callback to existing loading operation
        cached.callbacks.push(() => {
          if (mountedRef.current) {
            const finalCached = imageCache.get(imageUrl);
            if (finalCached?.status === 'loaded') {
              setImageSrc(imageUrl);
              setIsLoading(false);
              setHasError(false);
              onLoad?.();
            } else if (!isFallback && fallbackSrc && fallbackSrc !== imageUrl) {
              loadImage(fallbackSrc, true);
            } else {
              setHasError(true);
              setIsLoading(false);
              onError?.();
            }
          }
        });
        return;
      }
    }

    // Start new loading operation
    imageCache.set(imageUrl, { status: 'loading', callbacks: [] });
    
    const img = new Image();
    
    img.onload = () => {
      const cached = imageCache.get(imageUrl);
      imageCache.set(imageUrl, { status: 'loaded', callbacks: [] });
      
      if (mountedRef.current) {
        setImageSrc(imageUrl);
        setIsLoading(false);
        setHasError(false);
        onLoad?.();
      }
      
      // Execute callbacks
      cached?.callbacks.forEach(callback => callback());
    };
    
    img.onerror = () => {
      const cached = imageCache.get(imageUrl);
      imageCache.set(imageUrl, { status: 'error', callbacks: [] });
      
      if (!isFallback && fallbackSrc && fallbackSrc !== imageUrl) {
        loadImage(fallbackSrc, true);
      } else if (mountedRef.current) {
        setHasError(true);
        setIsLoading(false);
        onError?.();
      }
      
      // Execute callbacks
      cached?.callbacks.forEach(callback => callback());
    };
    
    img.src = imageUrl;
  }, [fallbackSrc, onLoad, onError]);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setImageSrc(null);
    
    const imageToLoad = src || fallbackSrc;
    
    if (!imageToLoad) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    loadImage(imageToLoad);
  }, [src, loadImage]);

  // Show loading placeholder
  if (isLoading) {
    return (
      <div 
        className={`flex items-center justify-center bg-muted animate-pulse ${className}`}
        style={style}
        {...props}
      >
        {placeholder || (
          <div className="text-center text-muted-foreground">
            <div className="text-2xl mb-2">🖼️</div>
            <div className="text-sm">Loading...</div>
          </div>
        )}
      </div>
    );
  }

  // Show error placeholder
  if (hasError || !imageSrc) {
    return (
      <div 
        className={`flex items-center justify-center bg-muted ${className}`}
        style={style}
        {...props}
      >
        {placeholder || (
          <div className="text-center text-muted-foreground">
            <div className="text-2xl mb-2">🚫</div>
            <div className="text-sm">Image unavailable</div>
          </div>
        )}
      </div>
    );
  }

  // Render the actual image
  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      className={className}
      style={style}
      onLoad={onLoad}
      onError={() => {
        setHasError(true);
        onError?.();
      }}
      {...props}
    />
  );
};

export default SafeImage;
