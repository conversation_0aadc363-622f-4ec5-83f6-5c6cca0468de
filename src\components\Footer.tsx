
import { Link } from "react-router-dom";
import { Shield, Contact, Send } from "lucide-react";
import { scrollToTop } from "@/utils/scrollToTop";

export default function Footer() {
  const handleLogoClick = () => {
    scrollToTop();
  };

  return (
    <footer className="bg-background/95 border-t border-border pt-8 sm:pt-12 pb-4 mt-12 sm:mt-16 text-muted-foreground font-sans relative shadow-inner rounded-t-2xl">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 flex flex-col gap-6 sm:gap-8 lg:gap-12">
        <div className="flex flex-col lg:flex-row gap-8 lg:gap-10 justify-between items-center lg:items-start">
          {/* Logo and description */}
          <div
            className="flex-1 min-w-[190px] flex flex-col items-center lg:items-start rounded-lg bg-background lg:pr-6"
            style={{
              background: "hsl(var(--background))",
              padding: 0,
              border: "none",
              margin: 0,
            }}
          >
            <Link
              to="/"
              onClick={handleLogoClick}
              className="transform transition-transform hover:scale-110 active:scale-95 cursor-pointer inline-block"
            >
              <img
                src="/lovable-uploads/c9c09943-f53b-4adf-b2a1-cbf6e9489467.png"
                alt="StreamDB Logo"
                className="h-[58px] w-auto mb-7 mt-3 transition-all duration-200 hover:drop-shadow-[0_0_15px_#e6cb8e] hover:brightness-110"
                style={{
                  background: "transparent",
                  filter:
                    "drop-shadow(0 0 11px #e6cb8e) drop-shadow(0 0 26px #e6cb8ec5)",
                  boxShadow: "none",
                  border: "none",
                  padding: 0,
                  margin: 0,
                  display: "block",
                }}
              />
            </Link>
            <p
              className="text-base leading-snug text-center lg:text-left max-w-xl mb-0 font-medium px-2 mt-5"
              style={{
                lineHeight: 1.48,
                color: "#a6b3c7", // matches the muted gray-blue from the screenshot
              }}
            >
              Your ultimate destination for streaming the latest movies and web series from around the world. Enjoy unlimited entertainment with our vast collection of content.
            </p>
          </div>

          {/* Columns - Reverted to original spacing */}
          <div className="flex-1 w-full px-2 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-4 lg:gap-6 text-center sm:text-left">
            <div className="flex flex-col items-center sm:items-start">
              <h4 className="font-semibold text-base mb-3 text-foreground">Legal</h4>
              <ul className="space-y-2 w-full flex flex-col items-center sm:items-start">
                <li className="w-full flex justify-center sm:justify-start">
                  <Link to="/disclaimer" className="flex items-center gap-2 hover:text-primary text-sm py-1" onClick={scrollToTop}>
                    <Shield className="w-4 h-4 text-primary flex-shrink-0" />
                    <span>Disclaimer</span>
                  </Link>
                </li>
                <li className="w-full flex justify-center sm:justify-start">
                  <Link to="/dmca" className="flex items-center gap-2 hover:text-primary text-sm py-1" onClick={scrollToTop}>
                    <Shield className="w-4 h-4 text-primary flex-shrink-0" />
                    <span>DMCA</span>
                  </Link>
                </li>
              </ul>
            </div>
            <div className="flex flex-col items-center sm:items-start">
              <h4 className="font-semibold text-base mb-3 text-foreground">Support</h4>
              <ul className="space-y-2 w-full flex flex-col items-center sm:items-start">
                <li className="w-full flex justify-center sm:justify-start">
                  <Link to="/contact" className="flex items-center gap-2 hover:text-primary text-sm py-1" onClick={scrollToTop}>
                    <Contact className="w-4 h-4 text-primary flex-shrink-0" />
                    <span>Contact Us</span>
                  </Link>
                </li>
              </ul>
            </div>
            <div className="flex flex-col items-center sm:items-start">
              <h4 className="font-semibold text-base mb-3 text-foreground">Community</h4>
              <ul className="space-y-2 w-full flex flex-col items-center sm:items-start">
                <li className="w-full flex justify-center sm:justify-start">
                  <a
                    href="https://t.me/thestreamdb"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 hover:text-primary text-sm py-1"
                  >
                    <Send className="w-4 h-4 text-primary flex-shrink-0" />
                    <span>Join our Telegram</span>
                  </a>
                </li>

              </ul>
            </div>
          </div>
        </div>

        {/* Horizontal line with subtle effect */}
        <div className="border-t border-border opacity-70 my-3 mx-0" />

        {/* Copyright and disclaimer */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-xs mb-0.5 text-center text-muted-foreground">
            © {new Date().getFullYear()} StreamDB. All rights reserved. | Made with <span className="text-red-500">♥️</span> for movie lovers
          </span>
          <div className="mt-1 text-[0.65rem] text-muted-foreground text-center max-w-2xl leading-5 px-2 py-2 bg-muted/20 backdrop-blur-md rounded-lg border border-border/70 shadow-sm">
            <span className="font-semibold">Disclaimer:</span>
            <br />
            "No Content Is Hosted on Our Server, all contents are provided by non-affiliated third parties. They are only indexed much like how Google works. Our Site does not accept responsibility for content hosted on third party websites and does not have any involvement in the downloading/uploading of movies. We just post links available in internet."
          </div>
        </div>
      </div>
    </footer>
  );
}
