
import { useState, useEffect } from "react";
import AddTitleForm from "@/components/admin/AddTitleForm";
import ContentManager from "@/components/admin/ContentManager";
import EnhancedContentManager from "@/components/admin/EnhancedContentManager";
import SectionsManager from "@/components/admin/SectionsManager";
import WebSeriesManager from "@/components/admin/WebSeriesManager";
import HeroCarouselManager from "@/components/admin/HeroCarouselManager";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Plus, Settings, ArrowUp, Loader2, LogOut, Shield, Clock, User, Folder, Key, Play } from "lucide-react";
import { Link } from "react-router-dom";
import { scrollToTop } from "@/utils/scrollToTop";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { PasswordResetModal } from "@/components/admin/PasswordResetModal";

export default function AdminPanel() {
  const { authState, logout, getTimeUntilExpiry } = useAuth();
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [sessionTimeLeft, setSessionTimeLeft] = useState(0);
  const [showPasswordReset, setShowPasswordReset] = useState(false);




  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);




  // Update session time left every minute
  useEffect(() => {
    const updateSessionTime = () => {
      const timeLeft = getTimeUntilExpiry();
      setSessionTimeLeft(timeLeft);
    };

    updateSessionTime();
    const interval = setInterval(updateSessionTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [getTimeUntilExpiry]);

  // Handle logout
  const handleLogout = () => {
    logout();
    toast("👋 Logged out successfully", {
      description: "You have been securely logged out of the admin panel"
    });
  };

  // Handle password reset completion
  const handlePasswordResetComplete = () => {
    toast("🔐 Password reset successfully", {
      description: "Your admin password has been updated. Please use your new password for future logins."
    });
  };

  // Format session time remaining
  const formatSessionTime = (milliseconds: number): string => {
    if (milliseconds <= 0) return "Expired";

    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };



  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      <Header />
      
      <main className="flex-1 p-0 bg-background/95 text-foreground flex flex-col items-center w-full">
        <div className="admin-panel-container w-full max-w-7xl mx-auto pt-4 sm:pt-10 pb-12 px-4 relative">
          {/* Back to Home Button and Auth Status - Non-overlapping positioning */}
          <div className="flex flex-col gap-2 mb-6 sm:mb-8 md:flex-row md:justify-between md:items-start md:mb-8">
            <Link to="/" onClick={scrollToTop}>
              <Button variant="outline" size="sm" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2 w-full md:w-auto justify-center">
                <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Back to Home</span>
                <span className="xs:hidden">Back</span>
              </Button>
            </Link>

            {/* Authentication Status Card - Non-overlapping positioning */}
            <Card className="admin-auth-card bg-background/95 border-primary/20 w-full md:w-auto md:min-w-[220px] lg:min-w-[280px] md:max-w-[320px]">
              <CardContent className="p-2 sm:p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-primary flex-shrink-0" />
                  <span className="text-xs font-medium truncate">Authenticated</span>
                </div>
                <div className="space-y-1 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="w-3 h-3 flex-shrink-0" />
                    <span className="truncate">{authState.user?.username}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3 flex-shrink-0" />
                    <span className="truncate">Session: {formatSessionTime(sessionTimeLeft)}</span>
                  </div>
                  <Badge variant="outline" className="text-xs px-1 py-0 w-fit">
                    {authState.user?.role}
                  </Badge>
                </div>
                <div className="space-y-2 mt-2">
                  <Button
                    onClick={() => setShowPasswordReset(true)}
                    variant="outline"
                    size="sm"
                    className="w-full text-xs py-1 min-h-[32px]"
                  >
                    <Key className="w-3 h-3 mr-1 flex-shrink-0" />
                    Reset Password
                  </Button>
                  <Button
                    onClick={handleLogout}
                    variant="destructive"
                    size="sm"
                    className="w-full text-xs py-1 min-h-[32px]"
                  >
                    <LogOut className="w-3 h-3 mr-1 flex-shrink-0" />
                    Logout
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Back to Top Button - Fixed position at bottom right */}
          {showBackToTop && (
            <div className="fixed bottom-2 right-2 sm:bottom-4 sm:right-4 z-10">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={scrollToTop}
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <ArrowUp className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Back to Top</span>
                <span className="xs:hidden">Top</span>
              </Button>
            </div>
          )}
          
          <div className="pt-4 sm:pt-0">
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-2 text-primary text-center px-2">Admin Panel</h1>
            <p className="text-muted-foreground mb-4 sm:mb-6 text-center max-w-xl mx-auto text-sm sm:text-base px-4 leading-relaxed">
              Manage movie & series entries directly or via TMDB. Add, edit, and preview new content to keep your streaming catalog up to date.
            </p>

            <div className="flex justify-center gap-3 mb-6 sm:mb-8 flex-wrap">
              <Link
                to="/admin/player-test"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                🎬 Test Video Player
              </Link>
              <Link
                to="/admin/tmdb-test"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                🎬 Test TMDB API
              </Link>
              {/* Diagnostic buttons removed */}
              <Link
                to="/admin/content-preview"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
              >
                👁️ Content Preview System
              </Link>
              <Link
                to="/admin/video-player-demo"
                className="inline-flex items-center gap-2 px-4 py-2 text-sm bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors"
              >
                🎮 Video Player Demo
              </Link>
            </div>

            <Tabs defaultValue="add-content" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-4 sm:mb-6 md:mb-8 h-auto">
                <TabsTrigger value="add-content" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2 sm:py-3 px-2 sm:px-4">
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="hidden sm:inline">Add New Content</span>
                  <span className="sm:hidden">Add New</span>
                </TabsTrigger>
                <TabsTrigger value="manage-content" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2 sm:py-3 px-2 sm:px-4">
                  <Settings className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="hidden sm:inline">Manage Content</span>
                  <span className="sm:hidden">Manage</span>
                </TabsTrigger>
                <TabsTrigger value="all-web-series" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2 sm:py-3 px-2 sm:px-4">
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="hidden sm:inline">All Web-Series</span>
                  <span className="sm:hidden">Series</span>
                </TabsTrigger>
                <TabsTrigger value="manage-sections" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2 sm:py-3 px-2 sm:px-4">
                  <Folder className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="hidden sm:inline">Manage Sections</span>
                  <span className="sm:hidden">Sections</span>
                </TabsTrigger>
                <TabsTrigger value="hero-carousel" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2 sm:py-3 px-2 sm:px-4">
                  <Play className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="hidden sm:inline">Hero Carousel</span>
                  <span className="sm:hidden">Carousel</span>
                </TabsTrigger>

              </TabsList>
              
              <TabsContent value="add-content" className="space-y-6">
                <AddTitleForm />
              </TabsContent>

              <TabsContent value="manage-content" className="space-y-6">
                <EnhancedContentManager />
              </TabsContent>

              <TabsContent value="all-web-series" className="space-y-6">
                <WebSeriesManager />
              </TabsContent>

              <TabsContent value="manage-sections" className="space-y-6">
                <SectionsManager />
              </TabsContent>

              <TabsContent value="hero-carousel" className="space-y-6">
                <HeroCarouselManager />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      <Footer />

      {/* Password Reset Modal */}
      <PasswordResetModal
        isOpen={showPasswordReset}
        onClose={() => setShowPasswordReset(false)}
        onResetComplete={handlePasswordResetComplete}
      />
    </div>
  );
}
