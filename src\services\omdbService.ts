/**
 * OMDb API Service
 *
 * Service for interacting with the Open Movie Database API
 * Handles movies and TV series search functionality
 */

// OMDb API Configuration
const OMDB_API_KEY = import.meta.env.VITE_OMDB_API_KEY || '';
const OMDB_BASE_URL = import.meta.env.VITE_OMDB_BASE_URL || 'http://www.omdbapi.com';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 200; // 5 requests per second with safety margin
let lastRequestTime = 0;

// Enhanced OMDb API Response Interfaces
export interface OMDbMovie {
  imdbID: string;
  Title: string;
  Year: string;
  Type: 'movie' | 'series' | 'episode';
  Poster: string;
  Plot?: string;
  Director?: string;
  Writer?: string;
  Actors?: string;
  Genre?: string;
  Runtime?: string;
  imdbRating?: string;
  Released?: string;
  Country?: string;
  Language?: string;
  Awards?: string;
  totalSeasons?: string;
  BoxOffice?: string;
  Metascore?: string;
  Rated?: string;
  DVD?: string;
  Website?: string;
  Production?: string;
  Producer?: string;
  Ratings?: Array<{
    Source: string;
    Value: string;
  }>;
}

export interface OMDbSearchResponse {
  Search: OMDbMovie[];
  totalResults: string;
  Response: string;
  Error?: string;
}

export interface OMDbDetailResponse extends OMDbMovie {
  Response: string;
  Error?: string;
}

/**
 * Rate limiting helper
 */
async function rateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();
}

/**
 * Make API request to OMDb
 */
async function makeOMDbRequest(params: Record<string, string>): Promise<any> {
  await rateLimit();

  if (!OMDB_API_KEY) {
    throw new Error('OMDb API key is not configured');
  }

  const url = new URL(OMDB_BASE_URL);
  url.searchParams.append('apikey', OMDB_API_KEY);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      url.searchParams.append(key, value);
    }
  });

  try {
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      throw new Error(`OMDb API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.Response === 'False') {
      throw new Error(data.Error || 'OMDb API request failed');
    }

    return data;
  } catch (error) {
    console.error('OMDb API Error:', error);
    throw error;
  }
}

/**
 * Search for movies and TV series
 */
export async function searchOMDbContent(
  query: string,
  type?: 'movie' | 'series',
  year?: string,
  page: number = 1
): Promise<OMDbSearchResponse> {
  const params: Record<string, string> = {
    s: query,
    page: page.toString()
  };

  if (type) {
    params.type = type;
  }

  if (year) {
    params.y = year;
  }

  return await makeOMDbRequest(params);
}

/**
 * Get detailed information by IMDb ID
 */
export async function getOMDbDetails(imdbId: string, plot: 'short' | 'full' = 'short'): Promise<OMDbDetailResponse> {
  const params: Record<string, string> = {
    i: imdbId,
    plot: plot
  };

  return await makeOMDbRequest(params);
}

/**
 * Get detailed information by title and year
 */
export async function getOMDbDetailsByTitle(
  title: string,
  year?: string,
  type?: 'movie' | 'series',
  plot: 'short' | 'full' = 'short'
): Promise<OMDbDetailResponse> {
  const params: Record<string, string> = {
    t: title,
    plot: plot
  };

  if (year) {
    params.y = year;
  }

  if (type) {
    params.type = type;
  }

  return await makeOMDbRequest(params);
}

/**
 * Helper function to determine if poster is available
 */
export function hasValidPoster(poster: string): boolean {
  return poster && poster !== 'N/A' && poster.startsWith('http');
}

/**
 * Helper function to format OMDb data for display with comprehensive field mapping
 */
export function formatOMDbData(movie: OMDbMovie) {
  // Extract and clean runtime
  const cleanRuntime = movie.Runtime && movie.Runtime !== 'N/A' ? 
    movie.Runtime.replace(/[^\d]/g, '') : '';
  
  // Extract and process genres
  const genreList = movie.Genre && movie.Genre !== 'N/A' ? 
    movie.Genre.split(', ').filter(g => g.trim()) : [];
  
  // Extract and process languages
  const languageList = movie.Language && movie.Language !== 'N/A' ? 
    movie.Language.split(', ').filter(l => l.trim()) : [];
  
  // Extract and process cast
  const castList = movie.Actors && movie.Actors !== 'N/A' ? 
    movie.Actors.split(', ').filter(a => a.trim()) : [];
  
  // Extract and process writers
  const writerList = movie.Writer && movie.Writer !== 'N/A' ? 
    movie.Writer.split(', ').filter(w => w.trim()) : [];
  
  // Extract director(s)
  const directorList = movie.Director && movie.Director !== 'N/A' ? 
    movie.Director.split(', ').filter(d => d.trim()) : [];
  
  // Extract country information
  const countryList = movie.Country && movie.Country !== 'N/A' ? 
    movie.Country.split(', ').filter(c => c.trim()) : [];
  
  // Determine studio from available data (prefer production companies over writers)
  let studio = '';
  if (movie.Director && movie.Director !== 'N/A') {
    // For movies, often the first production company or distributor
    studio = directorList[0] || '';
  }
  if (!studio && movie.Writer && movie.Writer !== 'N/A') {
    studio = writerList[0] || '';
  }
  
  // Enhanced studio detection - try multiple sources
  let enhancedStudio = studio;
  if (movie.Production && movie.Production !== 'N/A') {
    enhancedStudio = movie.Production;
  }
  
  // Enhanced rating extraction
  let imdbRating = '';
  if (movie.imdbRating && movie.imdbRating !== 'N/A') {
    imdbRating = movie.imdbRating;
  }
  
  // Extract additional ratings
  const ratings = movie.Ratings || [];
  const rottenTomatoes = ratings.find(r => r.Source?.includes('Rotten Tomatoes'))?.Value || '';
  const metacritic = ratings.find(r => r.Source?.includes('Metacritic'))?.Value || '';
  
  // Enhanced plot/description handling
  let description = '';
  if (movie.Plot && movie.Plot !== 'N/A' && movie.Plot !== 'Plot unknown.') {
    description = movie.Plot;
  }
  
  // Enhanced year extraction
  let year = '';
  if (movie.Year && movie.Year !== 'N/A') {
    const yearMatch = movie.Year.match(/(\d{4})/);
    year = yearMatch ? yearMatch[1] : movie.Year;
  }

  return {
    id: movie.imdbID,
    title: movie.Title || '',
    year: year,
    type: movie.Type,
    plot: description,
    description: description, // Alias for plot
    poster: hasValidPoster(movie.Poster) ? movie.Poster : '',
    posterUrl: hasValidPoster(movie.Poster) ? movie.Poster : '', // Alias for poster
    thumbnailUrl: hasValidPoster(movie.Poster) ? movie.Poster : '', // Use poster as thumbnail
    coverImage: hasValidPoster(movie.Poster) ? movie.Poster : '', // Use poster as cover image
    rating: imdbRating,
    imdbRating: imdbRating, // Alias for rating
    runtime: cleanRuntime,
    genres: genreList,
    director: directorList.join(', '),
    studio: enhancedStudio,
    languages: languageList,
    country: countryList.join(', '),
    actors: movie.Actors && movie.Actors !== 'N/A' ? movie.Actors : '',
    cast: castList, // Convert actors to array
    awards: movie.Awards && movie.Awards !== 'N/A' ? movie.Awards : '',
    released: movie.Released && movie.Released !== 'N/A' ? movie.Released : '',
    totalSeasons: movie.Type === 'series' && movie.totalSeasons && movie.totalSeasons !== 'N/A' ?
      parseInt(movie.totalSeasons) || null : null,
    trailer: '', // OMDb doesn't provide trailer URLs
    tags: genreList, // Use genres as tags
    // Enhanced crew information
    crew: [
      ...directorList.map(d => `${d} (Director)`),
      ...writerList.map(w => `${w} (Writer)`),
      ...(movie.Producer && movie.Producer !== 'N/A' ? 
          movie.Producer.split(/[,;]/).map(p => `${p.trim()} (Producer)`) : [])
    ],
    writers: writerList,
    producers: movie.Producer && movie.Producer !== 'N/A' ? 
      movie.Producer.split(/[,;]/).map(p => p.trim()).filter(p => p) : [],
    audioTracks: languageList, // Use languages as audio tracks
    quality: [], // OMDb doesn't provide quality info
    subtitleUrl: '', // OMDb doesn't provide subtitle info
    // Enhanced additional metadata
    boxOffice: movie.BoxOffice && movie.BoxOffice !== 'N/A' ? movie.BoxOffice : '',
    metascore: movie.Metascore && movie.Metascore !== 'N/A' ? movie.Metascore : '',
    rottenTomatoes: rottenTomatoes,
    metacritic: metacritic,
    rated: movie.Rated && movie.Rated !== 'N/A' ? movie.Rated : '',
    dvd: movie.DVD && movie.DVD !== 'N/A' ? movie.DVD : '',
    website: movie.Website && movie.Website !== 'N/A' ? movie.Website : '',
    production: movie.Production && movie.Production !== 'N/A' ? movie.Production : '',
    // Additional fields for better form population
    releaseDate: movie.Released && movie.Released !== 'N/A' ? movie.Released : '',
    contentRating: movie.Rated && movie.Rated !== 'N/A' ? movie.Rated : '',
    budget: movie.BoxOffice && movie.BoxOffice !== 'N/A' ? movie.BoxOffice : '',
  };
}

/**
 * Enhanced search by IMDb ID specifically with better error handling
 */
export async function searchOMDbByImdbId(imdbId: string): Promise<OMDbDetailResponse> {
  await rateLimit();

  if (!OMDB_API_KEY) {
    throw new Error('OMDb API key is not configured. Please check your environment variables.');
  }

  // Enhanced IMDb ID validation and cleaning
  let cleanId = imdbId.trim();
  
  // Handle different IMDb ID formats
  if (cleanId.startsWith('tt')) {
    cleanId = cleanId.substring(2);
  }
  
  // Validate IMDb ID format (should be numeric after 'tt')
  if (!/^\d+$/.test(cleanId)) {
    throw new Error('Invalid IMDb ID format. Expected format: tt1234567 or 1234567');
  }
  
  const formattedId = `tt${cleanId}`;
  
  try {
    const result = await getOMDbDetails(formattedId, 'full');
    
    // Validate that we received the expected data structure
    if (!result.imdbID || !result.Title) {
      throw new Error('Invalid response from OMDb API - missing required fields');
    }

    return result;
  } catch (error) {
    console.error('OMDb API Error:', {
      imdbId: formattedId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to fetch data from OMDb API');
  }
}

/**
 * Convert OMDb type to content type
 */
export function convertOMDbType(omdbType: string): 'movie' | 'tv' {
  return omdbType === 'series' ? 'tv' : 'movie';
}
