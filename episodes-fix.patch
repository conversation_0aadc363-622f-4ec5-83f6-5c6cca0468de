--- episodes.js.orig	2025-01-07 23:30:00.000000000 +0000
+++ episodes.js	2025-01-07 23:35:00.000000000 +0000
@@ -35,13 +35,13 @@
   try {
     const { contentId } = req.params;
 
-    const seasonsResult = await db.execute(`
+    const [seasonsRows] = await db.execute(`
       SELECT * FROM seasons
       WHERE content_id = ?
       ORDER BY season_number ASC
     `, [contentId]);
 
-    // db.execute returns rows directly, not [rows, fields]
-    const seasons = seasonsResult || [];
+    // db.execute returns [rows, fields], so we destructure to get rows
+    const seasons = seasonsRows || [];
 
     // Get episodes for each season
     for (let season of seasons || []) {
-      const episodesResult = await db.execute(`
+      const [episodesRows] = await db.execute(`
         SELECT * FROM episodes
         WHERE season_id = ?
         ORDER BY episode_number ASC
       `, [season.id]);
 
-      // db.execute returns rows directly, not [rows, fields]
-      season.episodes = episodesResult || [];
+      // db.execute returns [rows, fields], so we destructure to get rows
+      season.episodes = episodesRows || [];
     }
 
@@ -94,11 +94,11 @@
 
     // Check if season number already exists for this content
     try {
-      const existingSeasonResult = await db.execute(
+      const [existingSeasonRows] = await db.execute(
         'SELECT id FROM seasons WHERE content_id = ? AND season_number = ?',
         [contentId, seasonNumber]
       );
 
-      // Handle mysql2 result format properly
-      // db.execute returns rows directly, not [rows, fields]
-      const existingSeason = existingSeasonResult || [];
+      // Handle mysql2 result format properly  
+      // db.execute returns [rows, fields], so we destructure to get rows
+      const existingSeason = existingSeasonRows || [];
 
       if (existingSeason.length > 0) {
@@ -193,11 +193,11 @@
 
     try {
       // Get season info
-      const seasonResult = await db.execute('SELECT content_id FROM seasons WHERE id = ?', [seasonId]);
+      const [seasonRows] = await db.execute('SELECT content_id FROM seasons WHERE id = ?', [seasonId]);
 
       // Handle mysql2 result format properly
-      // db.execute returns rows directly, not [rows, fields]
-      const season = seasonResult || [];
+      // db.execute returns [rows, fields], so we destructure to get rows
+      const season = seasonRows || [];
 
       if (season.length === 0) {
@@ -212,11 +212,11 @@
       console.log('Found season for content:', contentId);
 
       // Check if episode number already exists for this season
-      const existingEpisodeResult = await db.execute(
+      const [existingEpisodeRows] = await db.execute(
         'SELECT id FROM episodes WHERE season_id = ? AND episode_number = ?',
         [seasonId, episodeNumber]
       );
 
-      // db.execute returns rows directly, not [rows, fields]
-      const existingEpisode = existingEpisodeResult || [];
+      // db.execute returns [rows, fields], so we destructure to get rows
+      const existingEpisode = existingEpisodeRows || [];
