/**
 * Dynamic Homepage Content Loader
 * Loads content sections dynamically based on database configuration
 */

import React from 'react';
import { MediaItem } from '@/types/media';
import apiService from '@/services/apiService';
import { ContentSection } from '@/types/sections';

export interface HomepageSection {
  id: number;
  name: string;
  slug: string;
  icon: string;
  color: string;
  content: MediaItem[];
  totalCount: number;
  showViewAll: boolean;
}

export interface HomepageData {
  sections: HomepageSection[];
  carouselContent: MediaItem[];
  isLoading: boolean;
  error: string | null;
}

/**
 * Load dynamic homepage content based on active sections
 */
export async function loadDynamicHomepageContent(): Promise<{
  success: boolean;
  data?: HomepageData;
  error?: string;
}> {
  try {
    // Get active sections that should show on homepage
    const sectionsResult = await apiService.getSections({
      active_only: true,
    });

    if (!sectionsResult.success) {
      throw new Error(sectionsResult.message || 'Failed to load sections');
    }

    const homepageSections = sectionsResult.data.filter(section => section.show_on_homepage);
    
    // Sort sections by display order
    homepageSections.sort((a, b) => a.display_order - b.display_order);

    const sections: HomepageSection[] = [];
    let carouselContent: MediaItem[] = [];

    // Load content for each section
    for (const section of homepageSections) {
      try {
        const contentResult = await apiService.getSectionContent(section.id, {
          page: 1,
          limit: section.max_items_homepage,
          published_only: true,
          sort_by: 'updated_at',
          sort_order: 'desc'
        });

        if (contentResult.success) {
          sections.push({
            id: section.id,
            name: section.name,
            slug: section.slug,
            icon: section.icon,
            color: section.color,
            content: contentResult.data,
            totalCount: contentResult.total,
            showViewAll: contentResult.total > section.max_items_homepage
          });

          // Skip collecting carousel content from sections - we'll load it separately
        }
      } catch (error) {
        console.error(`Failed to load content for section ${section.name}:`, error);
        // Continue with other sections even if one fails
      }
    }

    // Load carousel content using the public content API with carousel filter
    try {
      console.log('[Dynamic Homepage] Loading carousel content from public API...');
      const carouselResponse = await apiService.getContent({ carousel: 'true', limit: 50 });
      
      // Handle both direct array and wrapped response formats
      const carouselData = Array.isArray(carouselResponse) ? carouselResponse : 
                          (carouselResponse?.data || carouselResponse?.results || []);
      
      console.log(`[Dynamic Homepage] Loaded ${carouselData.length} carousel items from public API`);
      
      // Ensure proper ordering by carousel_position
      carouselContent = carouselData
        .filter(item => item && item.id && item.title && (item.add_to_carousel === 1 || item.addToCarousel === true))
        .sort((a, b) => {
          const posA = Number(a.carousel_position || a.carouselPosition || 999);
          const posB = Number(b.carousel_position || b.carouselPosition || 999);
          return posA - posB;
        })
        .slice(0, 10); // Take exactly 10 items
        
      console.log('[Dynamic Homepage] Final carousel items:', 
        carouselContent.map((item, i) => `${i+1}. ${item.title} (position: ${item.carousel_position || item.carouselPosition || 'unknown'})`));
    } catch (error) {
      console.error('[Dynamic Homepage] Failed to load carousel content:', error);
      carouselContent = []; // Fallback to empty array
    }

    return {
      success: true,
      data: {
        sections,
        carouselContent,
        isLoading: false,
        error: null
      }
    };
  } catch (error) {
    console.error('Failed to load dynamic homepage content:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to load homepage content'
    };
  }
}

/**
 * Shuffle array for random carousel content
 */
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Get fallback homepage content (for when dynamic loading fails)
 */
export async function getFallbackHomepageContent(allContent: MediaItem[]): Promise<HomepageData> {
  if (!Array.isArray(allContent)) {
    return {
      sections: [],
      carouselContent: [],
      isLoading: false,
      error: 'Invalid content data'
    };
  }

  try {
    const publishedContent = allContent.filter(item => item.isPublished !== false);
    const sortedContent = publishedContent.sort((a, b) => 
      new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime()
    );

    // Create fallback sections
    const sections: HomepageSection[] = [
      {
        id: 1,
        name: 'Movies',
        slug: 'movies',
        icon: 'Film',
        color: '#e11d48',
        content: sortedContent.filter(item => item.type === 'movie').slice(0, 20),
        totalCount: sortedContent.filter(item => item.type === 'movie').length,
        showViewAll: sortedContent.filter(item => item.type === 'movie').length > 20
      },
      {
        id: 2,
        name: 'Web Series',
        slug: 'series',
        icon: 'Tv',
        color: '#3b82f6',
        content: sortedContent.filter(item => item.type === 'series').slice(0, 20),
        totalCount: sortedContent.filter(item => item.type === 'series').length,
        showViewAll: sortedContent.filter(item => item.type === 'series').length > 20
      }
    ];

    // Get carousel content from featured items
    // Get carousel content from featured items - use simple filtering for fallback
    const carouselContent = sortedContent
      .filter(item => item.addToCarousel)
      .sort((a, b) => {
        const posA = Number(a.carousel_position || a.carouselPosition || 999);
        const posB = Number(b.carousel_position || b.carouselPosition || 999);
        return posA - posB;
      })
      .slice(0, 10);

    return {
      sections: sections.filter(section => section.content.length > 0),
      carouselContent,
      isLoading: false,
      error: null
    };
  } catch (error) {
    console.error('Error creating fallback homepage content:', error);
    return {
      sections: [],
      carouselContent: [],
      isLoading: false,
      error: 'Failed to process content'
    };
  }
}

/**
 * React hook for dynamic homepage content
 */
export function useDynamicHomepage() {
  const [homepageData, setHomepageData] = React.useState<HomepageData>({
    sections: [],
    carouselContent: [],
    isLoading: true,
    error: null
  });

  const loadContent = React.useCallback(async () => {
    setHomepageData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await loadDynamicHomepageContent();
      
      if (result.success && result.data) {
        setHomepageData(result.data);
      } else {
        setHomepageData(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Failed to load content'
        }));
      }
    } catch (error) {
      console.error('Error loading dynamic homepage:', error);
      setHomepageData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, []);

  React.useEffect(() => {
    loadContent();
  }, [loadContent]);

  return {
    ...homepageData,
    reload: loadContent
  };
}
