# StreamDB Online - Complete Project Documentation

## 🎯 Project Overview

StreamDB Online is a comprehensive streaming database website featuring movies and web series with an advanced admin panel for content management. The project uses a modern tech stack with React frontend, Node.js/Express backend, MySQL database, and a two-tier offshore VPS hosting setup with reverse proxy architecture.

## 🏗️ Architecture Overview

### System Architecture
```
User → Cloudflare → Reverse Proxy (*************) → Backend Server (***********)
                                                   ↓
                                              MySQL Database
```

### Technology Stack
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express.js + MySQL2
- **Database**: MySQL 8.0 with socket connections
- **Authentication**: JWT-based with session management
- **Process Management**: PM2
- **Reverse Proxy**: Nginx
- **Hosting**: Two-tier offshore VPS setup

## 🌐 Infrastructure Setup

### Server Configuration

#### Reverse Proxy Server (*************)
- **Purpose**: SSL termination, load balancing, security filtering
- **Services**: Nginx reverse proxy, Cloudflare integration
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Configuration**: `/etc/nginx/sites-available/streamdb-proxy`

#### Backend Server (***********)
- **Purpose**: Application hosting, database, file storage
- **Services**: Node.js app, MySQL, FastPanel2
- **Ports**: 3001 (Node.js), 8888 (FastPanel HTTPS), 3307 (MySQL Health)
- **Path**: `/var/www/streamdb_onl_usr/data/www/streamdb.online`

### Domain & SSL
- **Primary Domain**: streamdb.online
- **SSL**: Managed by Cloudflare
- **FastPanel Access**: fastpanel.streamdb.online:8888

## 🗄️ Database Architecture

### Database Configuration
- **Name**: `stream_db`
- **Connection**: Socket-based (`/var/run/mysqld/mysqld.sock`)
- **Charset**: utf8mb4_unicode_ci
- **Security**: Local-only access, no external exposure

### Core Tables
```sql
content              # Movies and web series
├── seasons          # Web series seasons
└── episodes         # Individual episodes

categories           # Content categorization
sections            # Homepage sections
admin_users         # Admin authentication
admin_sessions      # Session management
admin_security_logs # Security audit trail
```

### Key Relationships
- `content.id` → `seasons.content_id` (One-to-Many)
- `seasons.id` → `episodes.season_id` (One-to-Many)
- `content.category` → `categories.name` (Many-to-One)
- `content.section_id` → `sections.id` (Many-to-One)

## 🔧 Backend API Architecture

### Server Structure
```
server/
├── index.js                 # Main application entry
├── config/
│   └── database.js         # MySQL connection config
├── routes/
│   ├── auth.js            # Authentication endpoints
│   ├── content.js         # Content CRUD operations
│   ├── admin.js           # Admin panel operations
│   ├── categories.js      # Category management
│   ├── sections.js        # Homepage sections
│   └── episodes.js        # Web series episodes
├── middleware/
│   └── auth.js            # JWT authentication middleware
└── services/
    └── mysql-health.js    # Database health monitoring
```

### API Endpoints

#### Authentication (`/api/auth`)
- `POST /login` - Admin login with JWT
- `POST /logout` - Session termination
- `GET /verify` - Token validation
- `POST /change-password` - Password updates

#### Content Management (`/api/content`)
- `GET /` - Fetch all content with filtering
- `POST /` - Create new content
- `PUT /:id` - Update existing content
- `DELETE /:id` - Remove content
- `GET /:id` - Get specific content details

#### Episodes Management (`/api/episodes`)
- `GET /content/:contentId` - Get all seasons/episodes
- `POST /seasons` - Create new season
- `POST /episodes` - Create new episode
- `PUT /seasons/:id` - Update season
- `PUT /episodes/:id` - Update episode
- `DELETE /seasons/:id` - Remove season
- `DELETE /episodes/:id` - Remove episode

#### Admin Operations (`/api/admin`)
- `GET /stats` - Dashboard statistics
- `GET /security-logs` - Audit trail
- `POST /bulk-upload` - CSV data import

## ⚛️ Frontend Architecture

### Component Structure
```
src/
├── pages/
│   ├── Index.tsx              # Homepage with dynamic sections
│   ├── AdminPanel.tsx         # Admin dashboard
│   ├── ContentPage.tsx        # Individual content pages
│   ├── AllMovies.tsx          # Movies listing
│   └── AllSeries.tsx          # Web series listing
├── components/
│   ├── admin/
│   │   ├── AddTitleForm.tsx   # Content creation form
│   │   ├── ContentManager.tsx # Content management
│   │   ├── WebSeriesManager.tsx # Seasons/episodes management
│   │   └── SectionsManager.tsx # Homepage sections
│   ├── HeroCarousel.tsx       # Homepage carousel
│   ├── CardGrid.tsx           # Content grid display
│   ├── SecureVideoPlayer.tsx  # iFrame video player
│   └── Header.tsx             # Navigation header
├── contexts/
│   └── AuthContext.tsx        # Authentication state
└── services/
    └── apiService.ts          # API communication
```

### Key Features

#### Dynamic Homepage
- **Hero Carousel**: Maximum 10 featured items
- **Content Sections**: Database-driven sections (Movies, Web Series, etc.)
- **Responsive Design**: Mobile-first approach (320px-1024px)

#### Admin Panel
- **Secure Access**: JWT-based authentication
- **Content Management**: Full CRUD operations
- **Web Series Management**: Seasons and episodes
- **Section Management**: Homepage section configuration
- **TMDB/OMDb Integration**: Automatic metadata fetching

#### Video Player
- **Universal Embed Support**: All streaming platforms
- **Security**: Server-side embed link protection
- **Responsive**: Dynamic aspect ratio detection
- **Platform Detection**: Automatic player optimization

## 🔐 Security Implementation

### Authentication System
- **JWT Tokens**: Secure session management
- **Password Hashing**: bcrypt with 12 rounds
- **Session Storage**: Database-backed sessions
- **Role-Based Access**: Admin-only panel access

### Database Security
- **Socket Connections**: Local-only MySQL access
- **Prepared Statements**: SQL injection prevention
- **Input Validation**: Server-side data sanitization
- **Connection Pooling**: Resource management

### Network Security
- **Reverse Proxy**: Traffic filtering and SSL termination
- **Rate Limiting**: API endpoint protection
- **CORS Configuration**: Cross-origin request control
- **Security Headers**: XSS and clickjacking protection

## 🚀 Deployment Workflow

### Development to Production
1. **Local Development**: `G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB`
2. **Build Process**: `npm run build` (creates `dist/` folder)
3. **Backend Deployment**: Upload to `/var/www/streamdb_onl_usr/data/www/streamdb.online`
4. **Process Management**: PM2 restart and monitoring
5. **Frontend Deployment**: Static files served by Node.js

### PM2 Process Management
```bash
# Main application
pm2 start index.js --name "streamdb-main"

# MySQL health monitoring
pm2 start services/mysql-health.js --name "mysql-health"

# Process monitoring
pm2 status
pm2 logs
pm2 restart all
```

### Environment Configuration
```bash
# Production .env
NODE_ENV=production
PORT=3001
DB_SOCKET=/var/run/mysqld/mysqld.sock
JWT_SECRET=secure_jwt_secret
FRONTEND_URL=https://streamdb.online
```

## 📊 Content Management System

### Content Types
- **Movies**: Single video content with metadata
- **Web Series**: Multi-season content with episodes
- **Requested**: User-requested content tracking

### Metadata Fields
- Basic: Title, description, year, category
- Enhanced: TMDB ID, IMDB rating, runtime, studio
- Media: Poster, thumbnail, cover image, trailer
- Security: Encrypted video links, subtitle URLs

### Web Series Structure
```
Web Series
├── Season 1
│   ├── Episode 1 (Title + Video Link)
│   ├── Episode 2 (Title + Video Link)
│   └── ...
├── Season 2
│   └── ...
```

### Category System
18 predefined categories:
- Action, Adventure, Animation, Comedy, Crime
- Documentary, Drama, Family, Fantasy, History
- Horror, Music, Mystery, Romance, Science Fiction
- Thriller, War, Western

## 🔧 Maintenance & Monitoring

### Health Monitoring
- **Database Health**: Port 3307 health check service
- **Application Status**: PM2 process monitoring
- **Log Management**: Centralized logging system
- **Performance Metrics**: Resource usage tracking

### Backup Strategy
- **Database Backups**: Automated daily backups
- **File Backups**: Static assets and uploads
- **Configuration Backups**: Environment and config files
- **Rollback Capability**: Quick restoration procedures

### Update Procedures
1. **Code Updates**: Git-based deployment
2. **Database Migrations**: Schema update scripts
3. **Dependency Updates**: Package manager updates
4. **Security Patches**: Regular security updates

## 🎨 UI/UX Features

### Design System
- **Color Scheme**: Dark theme (#0a0a0a background, #e6cb8e primary)
- **Typography**: Optimized font weights for readability
- **Responsive Breakpoints**: 320px, 768px, 1024px, 1280px
- **Component Library**: Tailwind CSS + shadcn/ui

### User Experience
- **Search Functionality**: Global content search
- **Navigation**: Intuitive menu structure
- **Loading States**: Smooth transitions and feedback
- **Error Handling**: User-friendly error messages

## 📱 Mobile Optimization

### Responsive Design
- **Mobile-First**: Optimized for 320px+ screens
- **Touch-Friendly**: Large tap targets and gestures
- **Performance**: Optimized images and lazy loading
- **Navigation**: Collapsible mobile menu

### Video Player Mobile
- **Aspect Ratios**: Dynamic mobile-friendly ratios
- **Touch Controls**: Native mobile video controls
- **Fullscreen**: Seamless fullscreen experience
- **Performance**: Optimized for mobile bandwidth

## 🔄 Data Flow Architecture

### Frontend to Backend Communication
```
React Component → API Service → Express Route → Database Query → Response
     ↓              ↓              ↓              ↓              ↓
User Action → HTTP Request → Route Handler → MySQL Query → JSON Response
```

### Authentication Flow
```
1. User Login → JWT Token Generation → Session Storage
2. API Requests → Token Validation → Route Access
3. Token Expiry → Automatic Logout → Re-authentication
```

### Content Management Flow
```
Admin Panel → Form Submission → API Validation → Database Update → Cache Refresh → Frontend Update
```

## 🛠️ Development Environment Setup

### Local Development
```bash
# Clone repository
git clone <repository-url>
cd Streaming_DB

# Install dependencies
npm install

# Setup environment
cp server/.env.example server/.env
# Edit server/.env with local database credentials

# Start development servers
npm run dev          # Frontend (Vite)
cd server && npm run dev  # Backend (nodemon)
```

### Database Setup
```sql
-- Create database
CREATE DATABASE streamdb_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
SOURCE database/complete_schema.sql;

-- Import initial data
SOURCE database/initial_data.sql;
```

### Testing Environment
```bash
# Run tests
npm test

# Specific test files
npm test -- aspectRatioTest.ts
npm test -- embed-link-validation.test.ts
npm test -- iframe-config.test.ts
```

## 🔧 Configuration Management

### Environment Variables
```bash
# Database Configuration
DB_HOST=localhost
DB_USER=streamdb_user
DB_PASSWORD=secure_password
DB_NAME=streamdb_database
DB_SOCKET=/var/run/mysqld/mysqld.sock

# Security Configuration
JWT_SECRET=ultra_secure_jwt_secret_change_this
SESSION_SECRET=ultra_secure_session_secret_change_this
BCRYPT_ROUNDS=12

# API Configuration
TMDB_API_KEY=your_tmdb_api_key
OMDB_API_KEY=your_omdb_api_key

# Server Configuration
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://streamdb.online
```

### Nginx Configuration
```nginx
# Reverse Proxy (*************)
upstream backend {
    server ***********:3001;
}

server {
    listen 443 ssl http2;
    server_name streamdb.online;

    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        proxy_pass http://backend;
    }
}
```

## 📋 Operational Procedures

### Daily Operations
1. **Health Checks**: Monitor PM2 processes and database connectivity
2. **Log Review**: Check error logs for issues
3. **Performance Monitoring**: Review response times and resource usage
4. **Content Updates**: Process new content additions through admin panel

### Weekly Maintenance
1. **Database Optimization**: Run OPTIMIZE TABLE commands
2. **Log Rotation**: Archive and compress old log files
3. **Security Updates**: Apply system and dependency updates
4. **Backup Verification**: Test backup restoration procedures

### Emergency Procedures
1. **Service Restart**: `pm2 restart all`
2. **Database Recovery**: Restore from latest backup
3. **Rollback Deployment**: Use previous version backup
4. **Traffic Rerouting**: Cloudflare maintenance mode

## 🚨 Troubleshooting Guide

### Common Issues

#### Database Connection Errors
```bash
# Check MySQL status
systemctl status mysql

# Test database connection
node server/test-db-connection.js

# Check socket permissions
ls -la /var/run/mysqld/mysqld.sock
```

#### PM2 Process Issues
```bash
# Check process status
pm2 status

# View logs
pm2 logs streamdb-main

# Restart specific process
pm2 restart streamdb-main

# Full restart
pm2 restart all
```

#### Frontend Build Issues
```bash
# Clear cache and rebuild
rm -rf node_modules dist
npm install
npm run build

# Check build output
ls -la dist/
```

### Performance Optimization

#### Database Optimization
```sql
-- Index optimization
SHOW INDEX FROM content;
ANALYZE TABLE content, seasons, episodes;
OPTIMIZE TABLE content, seasons, episodes;

-- Query performance
EXPLAIN SELECT * FROM content WHERE category = 'Action';
```

#### Frontend Optimization
- **Image Optimization**: WebP format, lazy loading
- **Code Splitting**: Dynamic imports for large components
- **Caching**: Browser caching for static assets
- **Minification**: CSS and JavaScript compression

## 📊 Analytics & Monitoring

### Key Metrics
- **Response Times**: API endpoint performance
- **Database Queries**: Query execution times
- **User Engagement**: Content view statistics
- **Error Rates**: Application error frequency

### Monitoring Tools
- **PM2 Monitoring**: Process health and resource usage
- **MySQL Slow Query Log**: Database performance analysis
- **Nginx Access Logs**: Traffic patterns and errors
- **Custom Health Checks**: Application-specific monitoring

## 🔮 Future Enhancements

### Planned Features
1. **User Registration**: Public user accounts and profiles
2. **Content Rating**: User rating and review system
3. **Recommendation Engine**: AI-powered content suggestions
4. **Mobile App**: React Native mobile application
5. **CDN Integration**: Global content delivery network

### Technical Improvements
1. **Microservices**: Split monolith into smaller services
2. **Caching Layer**: Redis for improved performance
3. **Search Enhancement**: Elasticsearch integration
4. **Real-time Features**: WebSocket implementation
5. **API Versioning**: Backward-compatible API evolution

---

*This comprehensive documentation covers all aspects of the StreamDB Online project from architecture to operations. For specific implementation details, refer to the codebase and individual component documentation.*
