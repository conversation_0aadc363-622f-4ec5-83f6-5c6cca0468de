# 502 Bad Gateway Error - DIAGNOSIS & FIX

## 🔍 **Root Causes Identified:**

### **1. Database Connection Failure** ❌
- Server log shows: "Database connection failed"
- Server running in "static file mode" without database
- API routes won't work without database connection

### **2. Configuration Issues** ❌
- Using Linux socket path on Windows: `/var/run/mysqld/mysqld.sock`
- Should use TCP connection on Windows: `localhost:3306`

### **3. Missing Routes Registration** ❌
- New TMDB/OMDB routes not registered in server
- Server may crash on startup due to missing routes

## 🛠️ **IMMEDIATE FIXES REQUIRED:**

### **Fix 1: Update Database Configuration**
**File**: `server/.env`

**Change this line:**
```
DB_SOCKET=/var/run/mysqld/mysqld.sock  # Use socket for local server connections
```

**To:**
```
# DB_SOCKET=/var/run/mysqld/mysqld.sock  # Linux socket - disabled for Windows
```

### **Fix 2: Add Missing Routes**
**File**: `server/index.js`

**Add these lines after line 225** (after `app.use('/api/episodes', require('./routes/episodes'));`):
```javascript
app.use('/api/tmdb', require('./routes/tmdb'));
app.use('/api/omdb', require('./routes/omdb'));
```

### **Fix 3: Verify MySQL Service**
Check if MySQL is running on your server:

**On Windows:**
```cmd
services.msc
```
Look for "MySQL" service and ensure it's running.

**Or via Command Prompt:**
```cmd
net start mysql
```

### **Fix 4: Test Database Connection**
**Create test file**: `server/test-db-connection.js`
```javascript
require('dotenv').config();
const mysql = require('mysql2/promise');

async function testDB() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    await connection.execute('SELECT 1');
    console.log('✅ Database connection successful!');
    await connection.end();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  }
}

testDB();
```

## 🚀 **RESTART SEQUENCE:**

### **Step 1: Stop Current Server**
If server is running, stop it:
- Find the process and kill it
- Or restart your VPS/server

### **Step 2: Apply Fixes**
1. ✅ Comment out the socket line in `.env`
2. ✅ Add TMDB/OMDB routes to `index.js`
3. ✅ Ensure MySQL service is running

### **Step 3: Restart Server**
```bash
cd /path/to/server
npm start
# or
node index.js
# or
pm2 restart streamdb
```

### **Step 4: Verify**
Check these URLs:
- `https://streamdb.online/api/health` - Should return server status
- `https://streamdb.online/admin` - Should load admin panel
- `https://streamdb.online/api/tmdb/test` - Should test TMDB API
- `https://streamdb.online/api/omdb/test` - Should test OMDB API

## 🔧 **Additional Debugging:**

### **Check Server Logs:**
```bash
tail -f /path/to/server.log
# or
pm2 logs streamdb
```

### **Check Process Status:**
```bash
pm2 status
# or
ps aux | grep node
```

### **Check Port Usage:**
```bash
netstat -tulpn | grep :3001
# or
lsof -i :3001
```

## 🎯 **Expected Results After Fix:**

- ✅ Database connection successful
- ✅ Server starts without errors
- ✅ Admin panel accessible at `https://streamdb.online/admin`
- ✅ TMDB and OMDB APIs working via backend routes
- ✅ No more 502 Bad Gateway errors

## ⚠️ **If Still Having Issues:**

1. **Check MySQL credentials** in `.env` file
2. **Verify MySQL is running** on the server
3. **Check firewall settings** for port 3306
4. **Review server logs** for specific error messages
5. **Test database connection** separately

The 502 error will be resolved once the database connection is restored and the server restarts properly!