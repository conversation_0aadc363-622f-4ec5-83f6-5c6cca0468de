#!/usr/bin/env node

/**
 * Search Engine Notification Script for StreamDB
 * This script notifies search engines about sitemap updates.
 * It should be run after the sitemap is generated and the site is deployed.
 */

import https from 'https';
import { URL } from 'url';

console.log('🔔 Pinging search engines about sitemap updates...');

// The site URL and sitemap URL
const siteUrl = 'https://streamdb.online';
const sitemapUrl = `${siteUrl}/sitemap.xml`;

// Search engines to notify
const searchEngines = [
  {
    name: 'Google',
    url: `https://www.google.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`
  },
  {
    name: 'Bing',
    url: `https://www.bing.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`
  }
];

/**
 * Ping a search engine with the sitemap URL
 * @param {Object} engine - Search engine configuration
 */
function pingSearchEngine(engine) {
  return new Promise((resolve, reject) => {
    const url = new URL(engine.url);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: 'GET',
      timeout: 10000,
      headers: {
        'User-Agent': 'StreamDB-SitemapBot/1.0 (+https://streamdb.online)'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`✅ ${engine.name}: Successfully notified (Status: ${res.statusCode})`);
          resolve({ engine: engine.name, success: true, status: res.statusCode });
        } else {
          console.log(`⚠️  ${engine.name}: Received status ${res.statusCode}`);
          resolve({ engine: engine.name, success: false, status: res.statusCode });
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ ${engine.name}: Error - ${error.message}`);
      reject({ engine: engine.name, error: error.message });
    });

    req.on('timeout', () => {
      console.error(`⏰ ${engine.name}: Request timed out`);
      req.destroy();
      reject({ engine: engine.name, error: 'Request timeout' });
    });

    req.end();
  });
}

/**
 * Ping all search engines
 */
async function pingAllSearchEngines() {
  console.log(`📍 Notifying search engines about: ${sitemapUrl}`);
  
  const results = [];
  
  for (const engine of searchEngines) {
    try {
      const result = await pingSearchEngine(engine);
      results.push(result);
      
      // Add a small delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      results.push(error);
    }
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const total = searchEngines.length;
  
  console.log('\n📊 Summary:');
  console.log(`✅ Successfully notified: ${successful}/${total} search engines`);
  
  if (successful === total) {
    console.log('🎉 All search engines have been notified about the sitemap update!');
  } else {
    console.log('⚠️  Some notifications failed. Check the logs above for details.');
  }
  
  return results;
}

/**
 * Verify sitemap is accessible before pinging
 */
function verifySitemap() {
  return new Promise((resolve, reject) => {
    const url = new URL(sitemapUrl);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'HEAD',
      timeout: 5000
    };

    const req = https.request(options, (res) => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        console.log(`✅ Sitemap is accessible at ${sitemapUrl}`);
        resolve(true);
      } else {
        console.error(`❌ Sitemap not accessible: Status ${res.statusCode}`);
        reject(new Error(`Sitemap returned status ${res.statusCode}`));
      }
    });

    req.on('error', (error) => {
      console.error(`❌ Cannot access sitemap: ${error.message}`);
      reject(error);
    });

    req.on('timeout', () => {
      console.error('⏰ Sitemap verification timed out');
      req.destroy();
      reject(new Error('Sitemap verification timeout'));
    });

    req.end();
  });
}

// Main execution
async function main() {
  try {
    console.log('🔍 Verifying sitemap accessibility...');
    await verifySitemap();
    
    console.log('\n🚀 Starting search engine notifications...');
    await pingAllSearchEngines();
    
    console.log('\n✨ Search engine notification process completed!');
  } catch (error) {
    console.error(`\n💥 Process failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { pingAllSearchEngines, verifySitemap };