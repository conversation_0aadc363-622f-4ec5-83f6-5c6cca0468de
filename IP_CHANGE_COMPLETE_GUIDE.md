# 🔄 Complete Guide: Handling IP Changes in StreamDB Reverse Proxy Setup

## 📋 **OVERVIEW**

This guide provides step-by-step instructions for handling IP changes in your 4-tier reverse proxy setup when either or both VPS servers get new IP addresses.

### Current Setup
```
Client Browser
     ↓
Cloudflare (Free Plan)
     ↓
1st Offshore Reverse Proxy VPS: ************* (NGINX)
     ↓
2nd Offshore Backend VPS: *********** (FastPanel + MySQL + streamdb.online)
```

---

## 🎯 **SCENARIOS COVERED**

1. **Scenario A**: Only Reverse Proxy Server IP Changes (************* → NEW_IP)
2. **Scenario B**: Only Backend Server IP Changes (*********** → NEW_IP)
3. **Scenario C**: Both Server IPs Change

---

## 📝 **SCENARIO A: Reverse Proxy Server IP Changes**

### **When This Happens**
- Your reverse proxy VPS provider changes your IP from ************* to a new IP
- Backend server (***********) remains the same

### **What You Need**
- Access to Cloudflare dashboard
- SSH access to the NEW reverse proxy server
- SSH access to backend server (***********)

### **Step 1: Update Cloudflare DNS (CRITICAL - Do This First)**

1. **Login to Cloudflare Dashboard**
   - Go to: https://dash.cloudflare.com/
   - Select your `streamdb.online` domain

2. **Navigate to DNS Settings**
   - Click "DNS" in the left sidebar

3. **Update ALL DNS Records**
   - Find the `streamdb.online` A record
   - Click "Edit" and change IP from `*************` to `NEW_PROXY_IP`
   - Find the `www` A record  
   - Click "Edit" and change IP from `*************` to `NEW_PROXY_IP`
   - Find the `fastpanel` A record
   - Click "Edit" and change IP from `*************` to `NEW_PROXY_IP`
   - **IMPORTANT**: Keep all records as "Proxied" (Orange Cloud ☁️)

4. **Expected DNS Configuration After Update**
   ```
   Type | Name              | Content        | Proxy Status
   -----|-------------------|----------------|-------------
   A    | streamdb.online   | NEW_PROXY_IP   | Proxied ☁️
   A    | www               | NEW_PROXY_IP   | Proxied ☁️
   A    | fastpanel         | NEW_PROXY_IP   | Proxied ☁️
   ```

### **Step 2: Configure New Reverse Proxy Server**

1. **SSH into New Reverse Proxy Server**
   ```bash
   ssh root@NEW_PROXY_IP
   ```

2. **Install NGINX**
   ```bash
   # Update system
   apt update && apt upgrade -y
   
   # Install NGINX
   apt install nginx -y
   
   # Start and enable NGINX
   systemctl start nginx
   systemctl enable nginx
   ```

3. **Create SSL Certificate Directory**
   ```bash
   # Create SSL directories
   mkdir -p /etc/ssl/certs
   mkdir -p /etc/ssl/private
   
   # Set proper permissions
   chmod 755 /etc/ssl/certs
   chmod 700 /etc/ssl/private
   ```

4. **Create Cloudflare Origin Certificate**
   - Go to Cloudflare Dashboard → SSL/TLS → Origin Server
   - Click "Create Certificate"
   - Select "Let Cloudflare generate a private key and a CSR"
   - Add hostnames: `streamdb.online`, `*.streamdb.online`
   - Choose key type: RSA (2048)
   - Click "Create"
   - Copy the certificate and save as `/etc/ssl/certs/cloudflare-origin.pem`
   - Copy the private key and save as `/etc/ssl/private/cloudflare-origin.key`

   ```bash
   # Create certificate file
   nano /etc/ssl/certs/cloudflare-origin.pem
   # Paste the certificate content
   
   # Create private key file
   nano /etc/ssl/private/cloudflare-origin.key
   # Paste the private key content
   
   # Set proper permissions
   chmod 644 /etc/ssl/certs/cloudflare-origin.pem
   chmod 600 /etc/ssl/private/cloudflare-origin.key
   ```

5. **Create NGINX Configuration**
   ```bash
   # Remove default configuration
   rm -f /etc/nginx/sites-enabled/default
   
   # Create StreamDB configuration
   nano /etc/nginx/sites-available/streamdb.online
   ```

   **Copy this EXACT configuration:**
   ```nginx
   # WebSocket upgrade map
   map $http_upgrade $connection_upgrade {
       default upgrade;
       '' close;
   }

   # Rate limiting zones
   limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
   limit_req_zone $binary_remote_addr zone=admin:10m rate=5r/s;

   # Main website - HTTP to HTTPS redirect
   server {
       listen 80;
       server_name streamdb.online www.streamdb.online;
       return 301 https://$server_name$request_uri;
   }

   # Main website - HTTPS
   server {
       listen 443 ssl http2;
       server_name streamdb.online www.streamdb.online;
       
       # SSL Configuration
       ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
       ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
       
       # SSL Security Settings
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;
       ssl_session_cache shared:SSL:10m;
       ssl_session_timeout 10m;
       
       # Security Headers
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header X-XSS-Protection "1; mode=block" always;
       
       # API endpoints with rate limiting
       location /api/ {
           limit_req zone=api burst=20 nodelay;
           proxy_pass http://***********:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
       
       # Admin endpoints with stricter rate limiting
       location /admin {
           limit_req zone=admin burst=10 nodelay;
           proxy_pass http://***********:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
       
       # Static files and main application
       location / {
           proxy_pass http://***********:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_cache_bypass $http_upgrade;
           
           # Cache static assets
           location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
               proxy_pass http://***********:3001;
               expires 1y;
               add_header Cache-Control "public, immutable";
           }
       }
       
       # Health check endpoint
       location /health {
           proxy_pass http://***********:3001/api/health;
           access_log off;
       }
       
       # Block access to sensitive files
       location ~ /\. {
           deny all;
           access_log off;
           log_not_found off;
       }
       
       location ~ /(\.env|\.git|node_modules|server|database) {
           deny all;
           access_log off;
           log_not_found off;
       }
       
       # Logging
       access_log /var/log/nginx/streamdb_access.log;
       error_log /var/log/nginx/streamdb_error.log;
   }

   # FastPanel subdomain - HTTP to HTTPS redirect
   server {
       listen 80;
       server_name fastpanel.streamdb.online;
       return 301 https://$server_name$request_uri;
   }

   # FastPanel subdomain - HTTPS
   server {
       listen 443 ssl http2;
       server_name fastpanel.streamdb.online;
       
       # SSL Configuration (same as main site)
       ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
       ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
       
       # SSL Security Settings
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;
       ssl_session_cache shared:SSL:10m;
       ssl_session_timeout 10m;
       
       # Security Headers
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header X-XSS-Protection "1; mode=block" always;
       
       # Rate limiting for admin access
       limit_req_zone $binary_remote_addr zone=admin:10m rate=5r/s;
       
       # Proxy to FastPanel on backend server
       location / {
           limit_req zone=admin burst=50 nodelay;
           proxy_pass http://***********:8888;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection $connection_upgrade;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_set_header X-Forwarded-Host $host;
           proxy_set_header X-Forwarded-Port $server_port;
           proxy_set_header X-Scheme $scheme;
           proxy_cache_bypass $http_upgrade;
           
           # Disable proxy buffering for real-time responses
           proxy_buffering off;
           proxy_request_buffering off;
       }
       
       # Logging
       access_log /var/log/nginx/fastpanel_access.log;
       error_log /var/log/nginx/fastpanel_error.log;
   }
   ```

6. **Enable Configuration and Test**
   ```bash
   # Enable the site
   ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/
   
   # Test configuration
   nginx -t
   
   # If test passes, reload NGINX
   systemctl reload nginx
   
   # Check status
   systemctl status nginx
   ```

### **Step 3: Update Backend Server Configuration**

1. **SSH into Backend Server**
   ```bash
   ssh root@***********
   ```

2. **Update NGINX Configuration to Trust New Proxy IP**
   ```bash
   # Navigate to project directory
   cd /var/www/streamdb_onl_usr/data/www/streamdb.online
   
   # Backup current nginx config
   cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.$(date +%Y%m%d-%H%M%S)
   
   # Edit nginx configuration
   nano /etc/nginx/sites-available/streamdb.online
   ```

   **Find this line and update it:**
   ```nginx
   # OLD:
   set_real_ip_from *************;
   
   # NEW:
   set_real_ip_from NEW_PROXY_IP;
   ```

3. **Test and Reload Backend NGINX**
   ```bash
   # Test configuration
   nginx -t
   
   # Reload if test passes
   systemctl reload nginx
   ```

### **Step 4: Wait for DNS Propagation and Test**

1. **Wait for DNS Propagation (5-30 minutes)**
   ```bash
   # Check DNS resolution
   nslookup streamdb.online
   nslookup fastpanel.streamdb.online
   ```

2. **Test Website Access**
   ```bash
   # Test main website
   curl -I https://streamdb.online/
   
   # Test API
   curl -I https://streamdb.online/api/health
   
   # Test FastPanel
   curl -I https://fastpanel.streamdb.online/
   ```

3. **Expected Results**
   - All URLs should return HTTP 200 or appropriate responses
   - No SSL certificate errors
   - FastPanel should be accessible

---

## 📝 **SCENARIO B: Backend Server IP Changes**

### **When This Happens**
- Your backend VPS provider changes your IP from *********** to a new IP
- Reverse proxy server (*************) remains the same

### **What You Need**
- SSH access to reverse proxy server (*************)
- SSH access to the NEW backend server
- Database backup from old server (if applicable)

### **Step 1: Prepare New Backend Server**

1. **SSH into New Backend Server**
   ```bash
   ssh root@NEW_BACKEND_IP
   ```

2. **Install Required Software**
   ```bash
   # Update system
   apt update && apt upgrade -y
   
   # Install Node.js 18 LTS
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   apt-get install -y nodejs
   
   # Install PM2 globally
   npm install -g pm2
   
   # Install NGINX
   apt install nginx -y
   
   # Install MySQL
   apt install mysql-server -y
   ```

3. **Setup FastPanel (if using)**
   ```bash
   # Download and install FastPanel
   curl -sSL https://fastpanel.direct/install.sh | bash
   ```

### **Step 2: Migrate Application and Database**

1. **Create Project Directory**
   ```bash
   # Create directory structure
   mkdir -p /var/www/streamdb_onl_usr/data/www/streamdb.online
   cd /var/www/streamdb_onl_usr/data/www/streamdb.online
   ```

2. **Transfer Files from Old Server**
   ```bash
   # From your local machine or old server, copy files
   scp -r root@***********:/var/www/streamdb_onl_usr/data/www/streamdb.online/* root@NEW_BACKEND_IP:/var/www/streamdb_onl_usr/data/www/streamdb.online/
   ```

3. **Setup Database**
   ```bash
   # Secure MySQL installation
   mysql_secure_installation
   
   # Create database and user
   mysql -u root -p
   ```
   
   **In MySQL console:**
   ```sql
   CREATE DATABASE stream_db;
   CREATE USER 'stream_db_admin'@'localhost' IDENTIFIED BY 'Ohno@U2foundme';
   GRANT ALL PRIVILEGES ON stream_db.* TO 'stream_db_admin'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

4. **Import Database (if you have backup)**
   ```bash
   # Import database backup
   mysql -u stream_db_admin -p stream_db < database_backup.sql
   ```

### **Step 3: Configure Application**

1. **Install Dependencies**
   ```bash
   cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
   npm install --production
   ```

2. **Update Environment Variables**
   ```bash
   # The .env file should already be correct since it uses localhost for DB
   # Verify the configuration
   cat .env | grep -E "(DB_HOST|DB_USER|DB_PASSWORD|DB_NAME)"
   ```

3. **Start Application**
   ```bash
   # Start with PM2
   pm2 start index.js --name streamdb-online
   pm2 save
   pm2 startup
   ```

### **Step 4: Update Reverse Proxy Configuration**

1. **SSH into Reverse Proxy Server**
   ```bash
   ssh root@*************
   ```

2. **Update NGINX Configuration**
   ```bash
   # Backup current config
   cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.$(date +%Y%m%d-%H%M%S)
   
   # Edit configuration
   nano /etc/nginx/sites-available/streamdb.online
   ```

   **Find and replace ALL instances of the old IP:**
   ```nginx
   # OLD:
   proxy_pass http://***********:3001;
   proxy_pass http://***********:8888;
   
   # NEW:
   proxy_pass http://NEW_BACKEND_IP:3001;
   proxy_pass http://NEW_BACKEND_IP:8888;
   ```

3. **Test and Reload**
   ```bash
   # Test configuration
   nginx -t
   
   # Reload if test passes
   systemctl reload nginx
   ```

### **Step 5: Update Backend NGINX Configuration**

1. **Configure Backend NGINX**
   ```bash
   # On new backend server
   ssh root@NEW_BACKEND_IP
   
   # Create NGINX configuration
   nano /etc/nginx/sites-available/streamdb.online
   ```

   **Use this configuration:**
   ```nginx
   server {
       listen 80;
       server_name streamdb.online www.streamdb.online NEW_BACKEND_IP;
       
       # Document root for static files
       root /var/www/streamdb_onl_usr/data/www/streamdb.online/dist;
       index index.html;
       
       # Trust reverse proxy headers
       real_ip_header X-Forwarded-For;
       set_real_ip_from *************;
       
       # Serve static files
       location / {
           try_files $uri $uri/ /index.html;
       }
       
       # Proxy API requests to Node.js backend
       location /api/ {
           proxy_pass http://127.0.0.1:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
       
       # Admin panel
       location /admin {
           try_files $uri $uri/ /index.html;
       }
       
       # Health check
       location /health {
           proxy_pass http://127.0.0.1:3001/api/health;
           access_log off;
       }
   }
   ```

2. **Enable and Test**
   ```bash
   # Remove default
   rm -f /etc/nginx/sites-enabled/default
   
   # Enable site
   ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/
   
   # Test and reload
   nginx -t
   systemctl reload nginx
   ```

### **Step 6: Test Everything**

1. **Test Website**
   ```bash
   curl -I https://streamdb.online/
   curl -I https://streamdb.online/api/health
   ```

2. **Test FastPanel**
   ```bash
   curl -I https://fastpanel.streamdb.online/
   ```

3. **Check Application Logs**
   ```bash
   # On backend server
   pm2 logs streamdb-online
   ```

---

## 📝 **SCENARIO C: Both Server IPs Change**

### **When This Happens**
- Both your reverse proxy AND backend server IPs change
- This is the most complex scenario

### **What You Need**
- Access to Cloudflare dashboard
- SSH access to both NEW servers
- Database backup from old backend server

### **Step-by-Step Process**

1. **Follow Scenario A Steps 1-5** to setup the new reverse proxy server
2. **Follow Scenario B Steps 1-5** to setup the new backend server
3. **Update Cloudflare DNS** to point to the NEW reverse proxy IP
4. **Update reverse proxy configuration** to point to the NEW backend IP
5. **Test everything thoroughly**

### **Critical Configuration Updates**

1. **Cloudflare DNS Updates**
   ```
   streamdb.online → NEW_PROXY_IP
   www.streamdb.online → NEW_PROXY_IP  
   fastpanel.streamdb.online → NEW_PROXY_IP
   ```

2. **Reverse Proxy NGINX Updates**
   ```nginx
   # All proxy_pass directives should use NEW_BACKEND_IP
   proxy_pass http://NEW_BACKEND_IP:3001;
   proxy_pass http://NEW_BACKEND_IP:8888;
   ```

3. **Backend NGINX Updates**
   ```nginx
   # Trust the new reverse proxy IP
   set_real_ip_from NEW_PROXY_IP;
   ```

---

## ⚠️ **TROUBLESHOOTING COMMON ISSUES**

### **Issue 1: DNS Not Resolving**
**Symptoms**: Domain doesn't load, DNS errors
**Solutions**:
```bash
# Check DNS propagation
nslookup streamdb.online
dig streamdb.online

# Clear DNS cache (Windows)
ipconfig /flushdns

# Clear DNS cache (Linux)
sudo systemctl restart systemd-resolved

# Wait 30 minutes for global DNS propagation
```

### **Issue 2: SSL Certificate Errors**
**Symptoms**: "Your connection is not private" errors
**Solutions**:
1. Check Cloudflare SSL mode (should be "Flexible" or "Full")
2. Verify Cloudflare origin certificate is properly installed
3. Check NGINX SSL configuration

### **Issue 3: 502 Bad Gateway**
**Symptoms**: NGINX shows 502 error
**Solutions**:
```bash
# Check if backend service is running
ssh root@BACKEND_IP
pm2 status

# Check if backend is listening on correct port
netstat -tlnp | grep :3001

# Check NGINX error logs
tail -f /var/log/nginx/streamdb_error.log
```

### **Issue 4: FastPanel Not Accessible**
**Symptoms**: FastPanel subdomain doesn't work
**Solutions**:
```bash
# Check FastPanel status on backend server
systemctl status fastpanel2

# Check if port 8888 is listening
netstat -tlnp | grep :8888

# Restart FastPanel if needed
systemctl restart fastpanel2
```

### **Issue 5: Database Connection Errors**
**Symptoms**: API returns database connection errors
**Solutions**:
```bash
# Check MySQL status
systemctl status mysql

# Test database connection
mysql -u stream_db_admin -p stream_db

# Check .env file configuration
cat /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env | grep DB_
```

---

## 📋 **VERIFICATION CHECKLIST**

After completing any scenario, verify these items:

- [ ] DNS resolves correctly: `nslookup streamdb.online`
- [ ] Main website loads: `https://streamdb.online/`
- [ ] API responds: `https://streamdb.online/api/health`
- [ ] FastPanel accessible: `https://fastpanel.streamdb.online/`
- [ ] Admin panel works: `https://streamdb.online/admin`
- [ ] Database connections working
- [ ] PM2 processes running: `pm2 status`
- [ ] NGINX status good: `systemctl status nginx`
- [ ] No SSL certificate errors
- [ ] Mobile responsiveness maintained
- [ ] All existing functionality preserved

---

## 🚨 **EMERGENCY ROLLBACK PROCEDURES**

If something goes wrong during the IP change process:

### **For Cloudflare DNS Issues**
1. Immediately revert DNS records to old IPs in Cloudflare
2. Wait for DNS propagation (5-30 minutes)
3. Verify old setup is working

### **For Server Configuration Issues**
1. Restore NGINX configuration from backup:
   ```bash
   cp /etc/nginx/sites-available/streamdb.online.backup.* /etc/nginx/sites-available/streamdb.online
   nginx -t
   systemctl reload nginx
   ```

2. Restart services:
   ```bash
   pm2 restart streamdb-online
   systemctl restart nginx
   ```

### **For Database Issues**
1. Restore database from backup:
   ```bash
   mysql -u stream_db_admin -p stream_db < database_backup.sql
   ```

---

## 📞 **SUPPORT COMMANDS**

### **Check System Status**
```bash
# Check all services
systemctl status nginx
systemctl status mysql
pm2 status

# Check network connectivity
ping google.com
curl -I https://streamdb.online/

# Check DNS resolution
nslookup streamdb.online
dig streamdb.online
```

### **View Logs**
```bash
# NGINX logs
tail -f /var/log/nginx/streamdb_access.log
tail -f /var/log/nginx/streamdb_error.log

# PM2 logs
pm2 logs streamdb-online

# System logs
journalctl -u nginx -f
journalctl -u mysql -f
```

### **Test Connectivity**
```bash
# Test reverse proxy to backend
curl -I http://BACKEND_IP:3001/api/health

# Test FastPanel
curl -I http://BACKEND_IP:8888/

# Test database connection
mysql -u stream_db_admin -p stream_db -e "SELECT 1;"
```

---

## 🎯 **FINAL NOTES**

1. **Always backup configurations** before making changes
2. **Test in staging environment** if possible
3. **Have rollback plan ready** before starting
4. **Monitor logs** during and after changes
5. **Verify all functionality** after completion
6. **Document any custom changes** you make
7. **Keep this guide updated** with your specific configurations

**Remember**: IP changes can take 5-30 minutes to propagate globally. Be patient and don't panic if things don't work immediately.

---

## 📂 **YOUR CURRENT CONFIGURATION DETAILS**

### **Current Server Information**
- **Reverse Proxy Server**: *************
- **Backend Server**: ***********
- **Domain**: streamdb.online
- **Database**: stream_db (MySQL on localhost)
- **Node.js Port**: 3001
- **FastPanel Port**: 8888

### **Current File Locations**
- **Backend Project Path**: `/var/www/streamdb_onl_usr/data/www/streamdb.online`
- **NGINX Config**: `/etc/nginx/sites-available/streamdb.online`
- **Environment File**: `/var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env`
- **PM2 Process Name**: `streamdb-online`

### **Current Database Configuration**
```bash
DB_HOST=localhost
DB_PORT=3306
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=stream_db
DB_USER=stream_db_admin
DB_PASSWORD=Ohno@U2foundme
```

### **Current Environment Variables (Key Ones)**
```bash
FRONTEND_URL=https://streamdb.online
CORS_ORIGIN=https://streamdb.online,http://localhost:5173
PORT=3001
NODE_ENV=production
```

---

## 🔧 **AUTOMATED SCRIPTS FOR YOUR SETUP**

### **Script 1: Quick IP Change Checker**
Create this script to quickly verify your current setup:

```bash
#!/bin/bash
# save as: check-current-setup.sh

echo "=== StreamDB Current Setup Checker ==="
echo ""

echo "🌐 DNS Resolution:"
echo "streamdb.online resolves to:"
nslookup streamdb.online | grep "Address:" | tail -1

echo ""
echo "fastpanel.streamdb.online resolves to:"
nslookup fastpanel.streamdb.online | grep "Address:" | tail -1

echo ""
echo "🔗 Connectivity Tests:"
echo "Main website:"
curl -s -o /dev/null -w "%{http_code}" https://streamdb.online/
echo ""

echo "API health:"
curl -s -o /dev/null -w "%{http_code}" https://streamdb.online/api/health
echo ""

echo "FastPanel:"
curl -s -o /dev/null -w "%{http_code}" https://fastpanel.streamdb.online/
echo ""

echo ""
echo "📊 Current Server Status:"
if command -v pm2 &> /dev/null; then
    echo "PM2 Status:"
    pm2 status
else
    echo "PM2 not found on this server"
fi

echo ""
if command -v nginx &> /dev/null; then
    echo "NGINX Status:"
    systemctl status nginx --no-pager -l
else
    echo "NGINX not found on this server"
fi
```

### **Script 2: Reverse Proxy IP Update Script**
Create this script for when reverse proxy IP changes:

```bash
#!/bin/bash
# save as: update-reverse-proxy-ip.sh

# Configuration
OLD_PROXY_IP="*************"
NEW_PROXY_IP="$1"
BACKEND_IP="***********"

if [ -z "$NEW_PROXY_IP" ]; then
    echo "Usage: $0 <NEW_PROXY_IP>"
    echo "Example: $0 *************"
    exit 1
fi

echo "=== Updating Reverse Proxy IP ==="
echo "Old IP: $OLD_PROXY_IP"
echo "New IP: $NEW_PROXY_IP"
echo "Backend IP: $BACKEND_IP (unchanged)"
echo ""

# Update backend server to trust new proxy IP
echo "🔧 Updating backend server configuration..."
ssh root@$BACKEND_IP << EOF
    # Backup current config
    cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.\$(date +%Y%m%d-%H%M%S)

    # Update trusted IP
    sed -i 's/set_real_ip_from $OLD_PROXY_IP;/set_real_ip_from $NEW_PROXY_IP;/g' /etc/nginx/sites-available/streamdb.online

    # Test and reload
    nginx -t && systemctl reload nginx

    echo "✅ Backend server updated"
EOF

echo ""
echo "📋 Next Steps:"
echo "1. Update Cloudflare DNS records to point to: $NEW_PROXY_IP"
echo "2. Configure NGINX on new reverse proxy server: $NEW_PROXY_IP"
echo "3. Install SSL certificates on new reverse proxy server"
echo "4. Test all endpoints after DNS propagation"
```

### **Script 3: Backend IP Update Script**
Create this script for when backend IP changes:

```bash
#!/bin/bash
# save as: update-backend-ip.sh

# Configuration
PROXY_IP="*************"
OLD_BACKEND_IP="***********"
NEW_BACKEND_IP="$1"

if [ -z "$NEW_BACKEND_IP" ]; then
    echo "Usage: $0 <NEW_BACKEND_IP>"
    echo "Example: $0 *************"
    exit 1
fi

echo "=== Updating Backend IP ==="
echo "Proxy IP: $PROXY_IP (unchanged)"
echo "Old Backend IP: $OLD_BACKEND_IP"
echo "New Backend IP: $NEW_BACKEND_IP"
echo ""

# Update reverse proxy server configuration
echo "🔧 Updating reverse proxy server configuration..."
ssh root@$PROXY_IP << EOF
    # Backup current config
    cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.\$(date +%Y%m%d-%H%M%S)

    # Update all backend IP references
    sed -i 's/$OLD_BACKEND_IP/$NEW_BACKEND_IP/g' /etc/nginx/sites-available/streamdb.online

    # Test and reload
    nginx -t && systemctl reload nginx

    echo "✅ Reverse proxy server updated"
EOF

echo ""
echo "📋 Next Steps:"
echo "1. Setup new backend server at: $NEW_BACKEND_IP"
echo "2. Transfer application files and database"
echo "3. Configure NGINX on new backend server"
echo "4. Start PM2 processes"
echo "5. Test all endpoints"
```

---

## 📱 **MOBILE-FRIENDLY QUICK REFERENCE**

### **Emergency Contact Info**
- **Cloudflare Dashboard**: https://dash.cloudflare.com/
- **Current Reverse Proxy**: ssh root@*************
- **Current Backend**: ssh root@***********

### **Quick Commands**
```bash
# Check DNS
nslookup streamdb.online

# Test website
curl -I https://streamdb.online/

# Check PM2
pm2 status

# Check NGINX
systemctl status nginx

# View logs
pm2 logs streamdb-online
tail -f /var/log/nginx/streamdb_error.log
```

### **Emergency Rollback**
```bash
# Restore NGINX config
cp /etc/nginx/sites-available/streamdb.online.backup.* /etc/nginx/sites-available/streamdb.online
nginx -t && systemctl reload nginx

# Restart services
pm2 restart streamdb-online
```

---

## 🎯 **TESTING CHECKLIST AFTER IP CHANGES**

### **Immediate Tests (Do These First)**
- [ ] DNS resolves to new IP: `nslookup streamdb.online`
- [ ] Website loads: Open https://streamdb.online/ in browser
- [ ] API responds: `curl https://streamdb.online/api/health`
- [ ] No SSL errors in browser

### **Functional Tests**
- [ ] Admin panel accessible: https://streamdb.online/admin
- [ ] FastPanel accessible: https://fastpanel.streamdb.online/
- [ ] Database queries working (check admin panel)
- [ ] File uploads working (test in admin)
- [ ] Search functionality working

### **Performance Tests**
- [ ] Page load times acceptable
- [ ] API response times normal
- [ ] No 502/503 errors in logs
- [ ] Mobile responsiveness maintained

### **Security Tests**
- [ ] HTTPS redirects working
- [ ] Security headers present
- [ ] Rate limiting functional
- [ ] Admin access properly restricted

---

## 📞 **WHEN TO CALL FOR HELP**

### **Call for Help If:**
1. DNS doesn't resolve after 1 hour
2. SSL certificate errors persist
3. Database connection completely fails
4. Website shows 502/503 errors consistently
5. FastPanel becomes completely inaccessible
6. You see "Connection refused" errors

### **Information to Provide:**
1. Which scenario you're following (A, B, or C)
2. Current step you're on
3. Error messages (exact text)
4. Output of diagnostic commands
5. Screenshots of any browser errors

### **Diagnostic Commands to Run:**
```bash
# System status
systemctl status nginx mysql
pm2 status

# Network tests
ping google.com
curl -I https://streamdb.online/
nslookup streamdb.online

# Log files
tail -20 /var/log/nginx/streamdb_error.log
pm2 logs streamdb-online --lines 20
```

---

**🔥 CRITICAL REMINDER**: Always backup your configurations before making any changes. This guide assumes you have basic Linux command line knowledge. If you're not comfortable with any step, seek help before proceeding.
