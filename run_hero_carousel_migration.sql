-- Run this in your MySQL database to add Hero Carousel fields
-- You can execute this in phpMyAdmin or your MySQL client

USE stream_db;

-- Add carousel_position field to content table
ALTER TABLE content 
ADD COLUMN carousel_position INT DEFAULT NULL AFTER add_to_carousel;

-- Add crop_settings field to content table for poster positioning
ALTER TABLE content 
ADD COLUMN crop_settings JSON DEFAULT NULL AFTER carousel_position;

-- Add index for carousel_position for better performance
ALTER TABLE content 
ADD INDEX idx_carousel_position (carousel_position);

-- Update existing carousel items to have proper positions
SET @position = 0;
UPDATE content 
SET carousel_position = (@position := @position + 1) 
WHERE add_to_carousel = 1 
ORDER BY created_at ASC;

-- Verify the migration worked
SELECT 
    id, 
    title, 
    add_to_carousel, 
    carousel_position, 
    crop_settings,
    created_at 
FROM content 
WHERE add_to_carousel = 1 
ORDER BY carousel_position ASC;