#!/usr/bin/env node

/**
 * SEO Health Check for StreamDB
 * Comprehensive SEO analysis and ranking optimization checker
 */

import https from 'https';
import { URL } from 'url';

console.log('🔍 StreamDB SEO Health Check & Ranking Analysis\n');

const siteUrl = 'https://streamdb.online';
const results = {
  technical: {},
  content: {},
  performance: {},
  indexing: {}
};

/**
 * Check if sitemap is accessible and valid
 */
function checkSitemap() {
  return new Promise((resolve) => {
    console.log('📋 Checking sitemap.xml...');
    
    const url = new URL(`${siteUrl}/sitemap.xml`);
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'GET',
      timeout: 10000,
      headers: {
        'User-Agent': 'StreamDB-SEO-Checker/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          const isValidXml = data.includes('<?xml version="1.0"') && 
                            data.includes('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">');
          
          if (isValidXml) {
            const urlCount = (data.match(/<url>/g) || []).length;
            const hasHomepage = data.includes(`${siteUrl}</loc>`);
            const hasMovies = data.includes(`${siteUrl}/movies</loc>`);
            const hasSeries = data.includes(`${siteUrl}/series</loc>`);
            const hasAdmin = data.includes('/admin');
            
            results.technical.sitemap = {
              status: '✅',
              message: `Valid XML sitemap with ${urlCount} URLs`,
              details: {
                urlCount,
                hasHomepage,
                hasMovies,
                hasSeries,
                adminBlocked: !hasAdmin
              }
            };
          } else {
            results.technical.sitemap = {
              status: '❌',
              message: 'Sitemap exists but is not valid XML',
              details: { isHtml: data.includes('<html') }
            };
          }
        } else {
          results.technical.sitemap = {
            status: '❌',
            message: `Sitemap returned HTTP ${res.statusCode}`,
            details: { statusCode: res.statusCode }
          };
        }
        resolve();
      });
    });

    req.on('error', (error) => {
      results.technical.sitemap = {
        status: '❌',
        message: `Sitemap error: ${error.message}`,
        details: { error: error.message }
      };
      resolve();
    });

    req.on('timeout', () => {
      results.technical.sitemap = {
        status: '❌',
        message: 'Sitemap request timeout',
        details: { timeout: true }
      };
      req.destroy();
      resolve();
    });

    req.end();
  });
}

/**
 * Check robots.txt
 */
function checkRobotsTxt() {
  return new Promise((resolve) => {
    console.log('🤖 Checking robots.txt...');
    
    const url = new URL(`${siteUrl}/robots.txt`);
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'GET',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          const hasAdminBlock = data.includes('Disallow: /admin');
          const hasSitemapRef = data.includes('Sitemap: https://streamdb.online/sitemap.xml');
          const hasUserAgent = data.includes('User-agent:');
          
          if (hasAdminBlock && hasSitemapRef && hasUserAgent) {
            results.technical.robots = {
              status: '✅',
              message: 'Robots.txt properly configured',
              details: {
                adminBlocked: hasAdminBlock,
                sitemapReferenced: hasSitemapRef,
                hasUserAgent: hasUserAgent
              }
            };
          } else {
            results.technical.robots = {
              status: '⚠️',
              message: 'Robots.txt needs optimization',
              details: {
                adminBlocked: hasAdminBlock,
                sitemapReferenced: hasSitemapRef,
                hasUserAgent: hasUserAgent
              }
            };
          }
        } else {
          results.technical.robots = {
            status: '❌',
            message: `Robots.txt returned HTTP ${res.statusCode}`,
            details: { statusCode: res.statusCode }
          };
        }
        resolve();
      });
    });

    req.on('error', (error) => {
      results.technical.robots = {
        status: '❌',
        message: `Robots.txt error: ${error.message}`,
        details: { error: error.message }
      };
      resolve();
    });

    req.end();
  });
}

/**
 * Check homepage SEO
 */
function checkHomepageSEO() {
  return new Promise((resolve) => {
    console.log('🏠 Checking homepage SEO...');
    
    const url = new URL(siteUrl);
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          // Extract title
          const titleMatch = data.match(/<title[^>]*>([^<]+)<\/title>/i);
          const title = titleMatch ? titleMatch[1].trim() : '';
          
          // Extract meta description
          const descMatch = data.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
          const description = descMatch ? descMatch[1].trim() : '';
          
          // Check for Open Graph
          const hasOG = data.includes('property="og:');
          
          // Check for structured data
          const hasStructuredData = data.includes('application/ld+json');
          
          // Check for canonical
          const hasCanonical = data.includes('rel="canonical"');
          
          // Analyze title
          const titleLength = title.length;
          const titleOptimal = titleLength >= 30 && titleLength <= 60;
          const titleHasKeywords = title.toLowerCase().includes('movie') || 
                                  title.toLowerCase().includes('stream') ||
                                  title.toLowerCase().includes('series');
          
          // Analyze description
          const descLength = description.length;
          const descOptimal = descLength >= 120 && descLength <= 160;
          
          results.content.homepage = {
            status: titleOptimal && descOptimal && hasOG ? '✅' : '⚠️',
            message: 'Homepage SEO analysis complete',
            details: {
              title: {
                text: title,
                length: titleLength,
                optimal: titleOptimal,
                hasKeywords: titleHasKeywords
              },
              description: {
                text: description,
                length: descLength,
                optimal: descOptimal
              },
              hasOpenGraph: hasOG,
              hasStructuredData: hasStructuredData,
              hasCanonical: hasCanonical
            }
          };
        } else {
          results.content.homepage = {
            status: '❌',
            message: `Homepage returned HTTP ${res.statusCode}`,
            details: { statusCode: res.statusCode }
          };
        }
        resolve();
      });
    });

    req.on('error', (error) => {
      results.content.homepage = {
        status: '❌',
        message: `Homepage error: ${error.message}`,
        details: { error: error.message }
      };
      resolve();
    });

    req.end();
  });
}

/**
 * Check page speed (basic)
 */
function checkPageSpeed() {
  return new Promise((resolve) => {
    console.log('⚡ Checking page speed...');
    
    const startTime = Date.now();
    const url = new URL(siteUrl);
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'GET',
      timeout: 30000
    };

    const req = https.request(options, (res) => {
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const pageSize = Buffer.byteLength(data, 'utf8');
        const pageSizeKB = Math.round(pageSize / 1024);
        
        const speedStatus = loadTime < 3000 ? '✅' : loadTime < 5000 ? '⚠️' : '❌';
        
        results.performance.speed = {
          status: speedStatus,
          message: `Page loaded in ${loadTime}ms`,
          details: {
            loadTime: loadTime,
            pageSizeKB: pageSizeKB,
            optimal: loadTime < 3000
          }
        };
        resolve();
      });
    });

    req.on('error', (error) => {
      results.performance.speed = {
        status: '❌',
        message: `Speed test error: ${error.message}`,
        details: { error: error.message }
      };
      resolve();
    });

    req.end();
  });
}

/**
 * Check Google indexing status
 */
function checkGoogleIndexing() {
  return new Promise((resolve) => {
    console.log('🔍 Checking Google indexing...');
    
    // Check if site appears in Google search
    const searchQuery = `site:${siteUrl.replace('https://', '')}`;
    const googleUrl = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
    
    const url = new URL(googleUrl);
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: 'GET',
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const isIndexed = !data.includes('did not match any documents') && 
                         !data.includes('No results found');
        
        results.indexing.google = {
          status: isIndexed ? '✅' : '⚠️',
          message: isIndexed ? 'Site is indexed by Google' : 'Site may not be indexed yet',
          details: {
            indexed: isIndexed,
            searchQuery: searchQuery
          }
        };
        resolve();
      });
    });

    req.on('error', (error) => {
      results.indexing.google = {
        status: '⚠️',
        message: 'Could not check Google indexing',
        details: { error: error.message }
      };
      resolve();
    });

    req.end();
  });
}

/**
 * Generate SEO recommendations
 */
function generateRecommendations() {
  const recommendations = [];
  
  // Technical recommendations
  if (results.technical.sitemap?.status !== '✅') {
    recommendations.push({
      priority: 'HIGH',
      category: 'Technical',
      issue: 'Sitemap issues detected',
      action: 'Fix sitemap.xml to ensure proper XML format and URL inclusion'
    });
  }
  
  if (results.technical.robots?.status !== '✅') {
    recommendations.push({
      priority: 'HIGH',
      category: 'Technical',
      issue: 'Robots.txt needs optimization',
      action: 'Ensure admin blocking and sitemap reference in robots.txt'
    });
  }
  
  // Content recommendations
  if (results.content.homepage?.details?.title?.optimal === false) {
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Content',
      issue: 'Homepage title not optimal',
      action: 'Optimize title length (30-60 characters) and include target keywords'
    });
  }
  
  if (results.content.homepage?.details?.description?.optimal === false) {
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Content',
      issue: 'Meta description not optimal',
      action: 'Write compelling meta description (120-160 characters)'
    });
  }
  
  // Performance recommendations
  if (results.performance.speed?.details?.optimal === false) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Performance',
      issue: 'Page speed needs improvement',
      action: 'Optimize images, enable compression, minimize JavaScript'
    });
  }
  
  // Indexing recommendations
  if (results.indexing.google?.details?.indexed === false) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Indexing',
      issue: 'Site not indexed by Google',
      action: 'Submit sitemap to Google Search Console and request indexing'
    });
  }
  
  return recommendations;
}

/**
 * Main health check function
 */
async function runHealthCheck() {
  try {
    await Promise.all([
      checkSitemap(),
      checkRobotsTxt(),
      checkHomepageSEO(),
      checkPageSpeed(),
      checkGoogleIndexing()
    ]);
    
    console.log('\n📊 SEO Health Check Results:');
    console.log('============================\n');
    
    // Technical SEO
    console.log('🔧 Technical SEO:');
    console.log(`${results.technical.sitemap?.status || '❓'} Sitemap: ${results.technical.sitemap?.message || 'Not checked'}`);
    console.log(`${results.technical.robots?.status || '❓'} Robots.txt: ${results.technical.robots?.message || 'Not checked'}`);
    
    // Content SEO
    console.log('\n📝 Content SEO:');
    console.log(`${results.content.homepage?.status || '❓'} Homepage: ${results.content.homepage?.message || 'Not checked'}`);
    
    if (results.content.homepage?.details?.title) {
      const title = results.content.homepage.details.title;
      console.log(`   Title: "${title.text}" (${title.length} chars) ${title.optimal ? '✅' : '⚠️'}`);
    }
    
    if (results.content.homepage?.details?.description) {
      const desc = results.content.homepage.details.description;
      console.log(`   Description: "${desc.text.substring(0, 50)}..." (${desc.length} chars) ${desc.optimal ? '✅' : '⚠️'}`);
    }
    
    // Performance
    console.log('\n⚡ Performance:');
    console.log(`${results.performance.speed?.status || '❓'} Speed: ${results.performance.speed?.message || 'Not checked'}`);
    
    // Indexing
    console.log('\n🔍 Indexing:');
    console.log(`${results.indexing.google?.status || '❓'} Google: ${results.indexing.google?.message || 'Not checked'}`);
    
    // Recommendations
    const recommendations = generateRecommendations();
    
    if (recommendations.length > 0) {
      console.log('\n🎯 Recommendations:');
      console.log('===================');
      
      recommendations.forEach((rec, index) => {
        const priorityIcon = rec.priority === 'HIGH' ? '🔥' : rec.priority === 'MEDIUM' ? '⚠️' : 'ℹ️';
        console.log(`${index + 1}. ${priorityIcon} [${rec.priority}] ${rec.category}: ${rec.issue}`);
        console.log(`   Action: ${rec.action}\n`);
      });
    } else {
      console.log('\n🎉 Excellent! No major SEO issues detected.');
    }
    
    // Next steps
    console.log('\n🚀 Next Steps for Higher Rankings:');
    console.log('==================================');
    console.log('1. Set up Google Search Console (if not done)');
    console.log('2. Submit sitemap to search engines');
    console.log('3. Create high-quality content regularly');
    console.log('4. Build quality backlinks');
    console.log('5. Optimize for Core Web Vitals');
    console.log('6. Monitor keyword rankings');
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
}

// Run the health check
runHealthCheck();