const express = require('express');
const router = express.Router();
const { authenticateToken, requireModerator } = require('../middleware/auth');
const {
  getComprehensiveContentData,
  searchContent,
  isValidTMDBId,
  TMDBError
} = require('../services/tmdbService');

// Get comprehensive content data from TMDB
router.get('/content/:tmdbId', authenticateToken, async (req, res) => {
  try {
    const { tmdbId } = req.params;
    const { type } = req.query; // 'movie' or 'tv'

    if (!isValidTMDBId(tmdbId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid TMDB ID',
        message: 'TMDB ID must be a positive number'
      });
    }

    const result = await getComprehensiveContentData(tmdbId, type);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: 'TMDB Content Not Found',
        message: result.error
      });
    }

    res.json({
      success: true,
      data: result.data,
      contentType: result.contentType,
      tmdbId: tmdbId
    });

  } catch (error) {
    console.error('Error fetching TMDB content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch content from TMDB'
    });
  }
});

// Search TMDB content
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { query, type } = req.query;

    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Missing Query',
        message: 'Search query is required'
      });
    }

    const results = await searchContent(query.trim(), type);

    res.json({
      success: true,
      data: results.results || [],
      total: results.total_results || 0,
      page: results.page || 1,
      totalPages: results.total_pages || 1
    });

  } catch (error) {
    console.error('Error searching TMDB:', error);
    
    if (error instanceof TMDBError) {
      return res.status(400).json({
        success: false,
        error: 'TMDB API Error',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to search TMDB'
    });
  }
});

// Test TMDB API connection
router.get('/test', authenticateToken, async (req, res) => {
  try {
    // Test with a known movie ID (The Matrix)
    const result = await getComprehensiveContentData('603', 'movie');
    
    res.json({
      success: true,
      message: 'TMDB API connection successful',
      testData: {
        title: result.data?.title,
        hasImages: !!(result.data?.posterUrl && result.data?.thumbnailUrl),
        hasTrailer: !!result.data?.trailer
      }
    });

  } catch (error) {
    console.error('TMDB API test failed:', error);
    res.status(500).json({
      success: false,
      error: 'TMDB API Test Failed',
      message: error.message || 'Unknown error'
    });
  }
});

module.exports = router;
