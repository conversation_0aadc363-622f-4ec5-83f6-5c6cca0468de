# 🚨 QUICK FIX for 502 Bad Gateway Error

## ✅ **Fixes Already Applied:**

1. **Database Configuration Fixed** ✅
   - Commented out Linux socket path in `.env`
   - Now uses TCP connection for Windows

2. **Routes Added** ✅ (attempted)
   - TMDB and OMDB backend routes

## 🔧 **IMMEDIATE ACTIONS NEEDED:**

### **Step 1: Verify Routes Added**
Check if these lines exist in `server/index.js` after line 225:
```javascript
app.use('/api/tmdb', require('./routes/tmdb'));
app.use('/api/omdb', require('./routes/omdb'));
```

**If NOT present, add them manually after:**
```javascript
app.use('/api/episodes', require('./routes/episodes'));
```

### **Step 2: Restart Your Server**

**If using PM2 (most likely for production):**
```bash
pm2 restart streamdb
pm2 logs streamdb
```

**If running directly:**
```bash
cd server
node index.js
```

### **Step 3: Check MySQL Service**
Ensure MySQL is running on your server:
```bash
systemctl status mysql
# or
service mysql status
```

**If stopped, start it:**
```bash
systemctl start mysql
# or
service mysql start
```

## 🎯 **Root Cause:**
The 502 error is because:
1. **Database connection failed** (fixed by removing socket path)
2. **Server may have crashed** due to missing routes or DB issues
3. **Need to restart** to apply fixes

## 🔍 **Quick Test:**
After restart, check:
- `https://streamdb.online/api/health` - Should return server status
- `https://streamdb.online/admin` - Should load admin panel

## 📋 **If Still Having Issues:**

1. **Check server logs:**
   ```bash
   pm2 logs streamdb
   # or
   tail -f server.log
   ```

2. **Test database manually:**
   ```bash
   mysql -u stream_db_admin -p stream_db
   ```

3. **Check if server is running:**
   ```bash
   netstat -tulpn | grep :3001
   ```

The 502 error should resolve once the server restarts with the database connection working!