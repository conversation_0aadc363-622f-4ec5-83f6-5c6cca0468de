# Monetag Ads CSP Fix - Complete Solution

## Problem Identified
The Monetag ads are not loading due to Content Security Policy (CSP) restrictions blocking external scripts from:
- `https://pertawee.net/act/files/tag.min.js?z=9595337`
- `https://al5sm.com/tag.min.js`

## Root Cause
The nginx configuration files have restrictive CSP headers that don't allow these external ad script domains.

## Files Fixed
1. `nginx-streamdb-secure.conf` - Line 32
2. `nginx-reverse-proxy-config.conf` - Line 56

## Changes Made

### 1. nginx-streamdb-secure.conf
**BEFORE:**
```nginx
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

**AFTER:**
```nginx
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; connect-src 'self' https: wss: https://pertawee.net https://al5sm.com" always;
```

### 2. nginx-reverse-proxy-config.conf
**BEFORE:**
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;
```

**AFTER:**
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https: https://pertawee.net https://al5sm.com; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;
```

## Detailed Deployment Steps

### 🔍 **Pre-Deployment Checklist**
Before starting, ensure you have:
- SSH access to your reverse proxy server (*************)
- Root or sudo privileges
- Basic knowledge of which nginx config file is currently active

### 📋 **Step 1: Identify Current Nginx Configuration**

First, determine which configuration file is currently being used:

```bash
# SSH into reverse proxy server
ssh root@*************

# Check which configuration is currently active
ls -la /etc/nginx/sites-enabled/

# Check the content of the active configuration
cat /etc/nginx/sites-enabled/streamdb.online

# Look for the CSP line to identify which config you're using
grep -n "Content-Security-Policy" /etc/nginx/sites-enabled/streamdb.online
```

**Expected Output Analysis:**
- If you see: `"default-src 'self' http: https: data: blob: 'unsafe-inline'"` → You're using **nginx-streamdb-secure.conf**
- If you see: `"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval';"` → You're using **nginx-reverse-proxy-config.conf**

### 🛡️ **Step 2: Create Backup and Safety Measures**

```bash
# Create timestamped backup
sudo cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.$(date +%Y%m%d_%H%M%S)

# Verify backup was created
ls -la /etc/nginx/sites-available/streamdb.online.backup.*

# Create a rollback script for emergency use
cat > /tmp/nginx_rollback.sh << 'EOF'
#!/bin/bash
echo "Rolling back nginx configuration..."
LATEST_BACKUP=$(ls -t /etc/nginx/sites-available/streamdb.online.backup.* | head -1)
cp "$LATEST_BACKUP" /etc/nginx/sites-available/streamdb.online
nginx -t && systemctl reload nginx
echo "Rollback completed. Check nginx status:"
systemctl status nginx
EOF

chmod +x /tmp/nginx_rollback.sh
echo "Emergency rollback script created at: /tmp/nginx_rollback.sh"
```

### 📝 **Step 3A: Update Configuration (nginx-streamdb-secure.conf)**

If you're using the **nginx-streamdb-secure.conf** format:

```bash
# Open the configuration file for editing
sudo nano /etc/nginx/sites-available/streamdb.online

# Find line 32 (or search for Content-Security-Policy)
# Replace this line:
# add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

# With this line:
# add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; connect-src 'self' https: wss: https://pertawee.net https://al5sm.com" always;
```

**Or use sed command for automatic replacement:**
```bash
sudo sed -i 's|add_header Content-Security-Policy "default-src '\''self'\'' http: https: data: blob: '\''unsafe-inline'\''" always;|add_header Content-Security-Policy "default-src '\''self'\'' http: https: data: blob: '\''unsafe-inline'\''; script-src '\''self'\'' '\''unsafe-inline'\'' '\''unsafe-eval'\'' https://pertawee.net https://al5sm.com; connect-src '\''self'\'' https: wss: https://pertawee.net https://al5sm.com" always;|g' /etc/nginx/sites-available/streamdb.online
```

### 📝 **Step 3B: Update Configuration (nginx-reverse-proxy-config.conf)**

If you're using the **nginx-reverse-proxy-config.conf** format:

```bash
# Open the configuration file for editing
sudo nano /etc/nginx/sites-available/streamdb.online

# Find line 56 (or search for Content-Security-Policy)
# Replace this line:
# add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;

# With this line:
# add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https: https://pertawee.net https://al5sm.com; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;
```

**Or use sed command for automatic replacement:**
```bash
sudo sed -i 's|script-src '\''self'\'' '\''unsafe-inline'\'' '\''unsafe-eval'\'';|script-src '\''self'\'' '\''unsafe-inline'\'' '\''unsafe-eval'\'' https://pertawee.net https://al5sm.com;|g' /etc/nginx/sites-available/streamdb.online

sudo sed -i 's|connect-src '\''self'\'' https:;|connect-src '\''self'\'' https: https://pertawee.net https://al5sm.com;|g' /etc/nginx/sites-available/streamdb.online
```

### ✅ **Step 4: Verify Configuration Changes**

```bash
# Display the updated CSP line to verify changes
echo "=== Updated CSP Configuration ==="
grep -A 1 -B 1 "Content-Security-Policy" /etc/nginx/sites-available/streamdb.online

# Test nginx configuration syntax
echo "=== Testing Nginx Configuration ==="
sudo nginx -t

# Check for any syntax errors
if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration test PASSED"
else
    echo "❌ Nginx configuration test FAILED - Check syntax errors above"
    echo "Run rollback script if needed: /tmp/nginx_rollback.sh"
    exit 1
fi
```

### 🔄 **Step 5: Apply Changes**

```bash
# Reload nginx with new configuration
echo "=== Applying Nginx Configuration ==="
sudo systemctl reload nginx

# Check reload status
if [ $? -eq 0 ]; then
    echo "✅ Nginx reload SUCCESSFUL"
else
    echo "❌ Nginx reload FAILED"
    echo "Running automatic rollback..."
    /tmp/nginx_rollback.sh
    exit 1
fi

# Verify nginx is running properly
echo "=== Nginx Status Check ==="
sudo systemctl status nginx --no-pager -l

# Check if nginx is actively serving requests
echo "=== Testing Nginx Response ==="
curl -I http://localhost/ 2>/dev/null | head -1
```

### 🌐 **Step 6: Verify Fix from Client Side**

**6.1 Clear Browser Cache:**
```bash
# Instructions for different browsers:
# Chrome: Ctrl+Shift+Delete → Select "All time" → Check all boxes → Clear data
# Firefox: Ctrl+Shift+Delete → Select "Everything" → Check all boxes → Clear Now
# Safari: Develop menu → Empty Caches (or Cmd+Option+E)
# Edge: Ctrl+Shift+Delete → Select "All time" → Check all boxes → Clear now
```

**6.2 Test Website Access:**
1. Open browser and navigate to `https://streamdb.online`
2. Open Developer Tools (F12)
3. Go to Console tab
4. Refresh the page (Ctrl+F5 for hard refresh)

**6.3 Check for CSP Errors:**
```javascript
// In browser console, you should NOT see these errors anymore:
// "Refused to load the script 'https://pertawee.net/act/files/tag.min.js?z=9595337' because it violates the following Content Security Policy directive"
// "Refused to load the script 'https://al5sm.com/tag.min.js' because it violates the following Content Security Policy directive"
```

**6.4 Verify Script Loading:**
1. Go to Network tab in Developer Tools
2. Filter by "JS" or search for "pertawee" and "al5sm"
3. Refresh the page
4. You should see successful (200 OK) requests to:
   - `https://pertawee.net/act/files/tag.min.js?z=9595337`
   - `https://al5sm.com/tag.min.js`

### 🎯 **Step 7: Test Ad Functionality**

**7.1 Test PopUnder Ads:**
1. Navigate to a content page (not admin pages)
2. Click on any content/link
3. PopUnder ads should trigger (new window/tab may open)

**7.2 Test Push Notification Ads:**
1. Stay on the website for a few minutes
2. Push notification requests should appear
3. Check browser's notification settings

**7.3 Verify Admin Pages Remain Ad-Free:**
1. Navigate to `/admin` or `/login`
2. No ads should load (this is intentional)
3. MonetizationManager excludes admin routes

### 🔧 **Step 8: Post-Deployment Monitoring**

```bash
# Monitor nginx error logs for any issues
sudo tail -f /var/log/nginx/error.log &
TAIL_PID=$!

# Monitor access logs to see if ads are being requested
sudo tail -f /var/log/nginx/access.log | grep -E "(pertawee|al5sm)" &
ACCESS_PID=$!

echo "Monitoring logs... Press Ctrl+C to stop"
echo "Error log PID: $TAIL_PID"
echo "Access log PID: $ACCESS_PID"

# Let it run for a few minutes, then stop monitoring
sleep 300
kill $TAIL_PID $ACCESS_PID 2>/dev/null
```

### 🚨 **Emergency Rollback Procedure**

If anything goes wrong:

```bash
# Quick rollback
/tmp/nginx_rollback.sh

# Or manual rollback
LATEST_BACKUP=$(ls -t /etc/nginx/sites-available/streamdb.online.backup.* | head -1)
sudo cp "$LATEST_BACKUP" /etc/nginx/sites-available/streamdb.online
sudo nginx -t && sudo systemctl reload nginx

# Verify rollback
sudo systemctl status nginx
curl -I https://streamdb.online/
```

### 📊 **Success Indicators**

✅ **Configuration Applied Successfully:**
- `nginx -t` returns no errors
- `systemctl reload nginx` succeeds
- Website loads normally
- No CSP errors in browser console

✅ **Ads Working:**
- Monetag scripts load successfully (Network tab)
- PopUnder ads trigger on content clicks
- Push notifications appear
- Admin pages remain ad-free

✅ **Security Maintained:**
- All other security headers intact
- Reverse proxy routing unchanged
- Backend server remains protected
- SSL/TLS configuration unaffected

## Security Considerations
The CSP changes are minimal and only allow:
- **script-src**: Added specific Monetag domains for ad scripts
- **connect-src**: Added Monetag domains for API calls/tracking

This maintains security while allowing legitimate ad functionality.

## Troubleshooting

### If ads still don't work after CSP fix:
1. Check browser console for any remaining errors
2. Verify MonetizationManager.tsx is properly loaded
3. Check if ad blockers are interfering
4. Ensure you're testing on non-admin pages

### If nginx fails to reload:
```bash
# Check nginx error logs
tail -f /var/log/nginx/error.log

# Check configuration syntax
nginx -t

# If needed, restore backup
cp /etc/nginx/sites-available/streamdb.online.backup /etc/nginx/sites-available/streamdb.online
systemctl reload nginx
```

## Files Modified in This Fix
- ✅ `nginx-streamdb-secure.conf`
- ✅ `nginx-reverse-proxy-config.conf`
- ✅ `src/App.tsx` (already has MonetizationManager)
- ✅ `src/components/MonetizationManager.tsx` (already implemented)

## Expected Result
After deploying this fix:
- ❌ CSP errors in browser console will disappear
- ✅ Monetag scripts will load successfully
- ✅ PopUnder ads will work on content clicks
- ✅ Push notification ads will be active
- ✅ Admin pages remain ad-free as intended

## Final Notes
This is a **complete fix** for the Monetag ads CSP issue. The problem was entirely due to nginx CSP restrictions, not the React implementation. The MonetizationManager component was correctly implemented - it just couldn't load the external scripts due to CSP blocking.