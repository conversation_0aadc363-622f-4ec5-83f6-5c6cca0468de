#!/usr/bin/env node

/**
 * Fix Diagnostic System Syntax Issues
 * This script fixes any syntax issues in the diagnostic system files
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing StreamDB Database Diagnostic System Syntax Issues...\n');

// Fix database-diagnostic.js syntax issue
const diagnosticFile = 'server/services/database-diagnostic.js';

if (fs.existsSync(diagnosticFile)) {
  console.log('📝 Fixing database-diagnostic.js...');
  
  let content = fs.readFileSync(diagnosticFile, 'utf8');
  
  // Fix the double closing brace issue
  content = content.replace(/    }\s*}\s*\n\s*\/\*\*\s*\n\s*\* Monitor connection pool/g, 
    '    }\n  }\n\n  /**\n   * Monitor connection pool');
  
  // Ensure proper class structure
  if (!content.includes('module.exports = DatabaseDiagnostic;')) {
    content += '\n\nmodule.exports = DatabaseDiagnostic;\n';
  }
  
  fs.writeFileSync(diagnosticFile, content);
  console.log('✅ Fixed database-diagnostic.js');
} else {
  console.log('❌ database-diagnostic.js not found');
}

// Verify all required files exist
const requiredFiles = [
  'server/services/database-diagnostic.js',
  'server/services/database-monitor.js',
  'server/services/database-error-classifier.js',
  'server/routes/database-diagnostic.js',
  'server/scripts/run-database-diagnostic.js'
];

console.log('\n📋 Verifying required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (allFilesExist) {
  console.log('\n🎉 All diagnostic system files are present and fixed!');
  console.log('\nYou can now run:');
  console.log('  node server/scripts/run-database-diagnostic.js --quick');
} else {
  console.log('\n⚠️  Some files are missing. Please ensure all diagnostic files are uploaded to the server.');
}

// Test basic Node.js syntax
console.log('\n🧪 Testing syntax...');
try {
  require('./server/services/database-diagnostic.js');
  console.log('✅ database-diagnostic.js syntax is valid');
} catch (error) {
  console.log('❌ database-diagnostic.js syntax error:', error.message);
}

console.log('\n✅ Syntax fix completed!');
