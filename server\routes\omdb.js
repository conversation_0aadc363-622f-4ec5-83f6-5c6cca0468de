const express = require('express');
const router = express.Router();
const { authenticateToken, requireModerator } = require('../middleware/auth');
const {
  getContentByImdbId,
  searchContent,
  isValidImdbId,
  OMDbError
} = require('../services/omdbService');


// Get detailed information by IMDb ID
router.get('/content/:imdbId', authenticateToken, async (req, res) => {
  try {
    const { imdbId } = req.params;
    
    if (!isValidImdbId(imdbId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid IMDb ID',
        message: 'IMDb ID must be in format tt1234567 or 1234567'
      });
    }

    const result = await getContentByImdbId(imdbId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: 'OMDb Content Not Found',
        message: result.error
      });
    }

    res.json(result);

  } catch (error) {
    console.error('Error fetching OMDb content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: error.message || 'Failed to fetch content from OMDb'
    });
  }
});

// Search OMDb content
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { query, type, year, page = 1 } = req.query;

    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Missing Query',
        message: 'Search query is required'
      });
    }

    const result = await searchContent(query.trim(), type, year, page);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: 'OMDb API Error',
        message: result.error
      });
    }

    res.json(result);

  } catch (error) {
    console.error('Error searching OMDb:', error);
    
    if (error instanceof OMDbError) {
      return res.status(400).json({
        success: false,
        error: 'OMDb API Error',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: error.message || 'Failed to search OMDb'
    });
  }
});

// Test OMDb API connection
router.get('/test', authenticateToken, async (req, res) => {
  try {
    // Test with a known movie ID (The Matrix)
    const result = await getContentByImdbId('tt0133093');
    
    if (!result.success) {
      throw new Error(result.error);
    }
    
    res.json({
      success: true,
      message: 'OMDb API connection successful',
      testData: {
        title: result.data.title,
        year: result.data.year,
        type: result.data.type,
        hasPlot: !!result.data.plot,
        hasPoster: !!result.data.poster
      }
    });

  } catch (error) {
    console.error('OMDb API test failed:', error);
    res.status(500).json({
      success: false,
      error: 'OMDb API Test Failed',
      message: error.message || 'Unknown error'
    });
  }
});

module.exports = router;