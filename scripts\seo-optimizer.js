#!/usr/bin/env node

/**
 * SEO Optimizer for StreamDB
 * Comprehensive SEO analysis and optimization tool
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 StreamDB SEO Optimizer & Ranking Booster\n');

const siteUrl = 'https://streamdb.online';
const publicDir = path.join(__dirname, '..', 'public');
const distDir = path.join(__dirname, '..', 'dist');

// SEO optimization tasks
const optimizations = {
  sitemap: false,
  robots: false,
  metaTags: false,
  structuredData: false,
  performance: false,
  security: false
};

/**
 * Generate enhanced robots.txt with better SEO directives
 */
function optimizeRobotsTxt() {
  console.log('🤖 Optimizing robots.txt...');
  
  const robotsContent = `# StreamDB.online - Enhanced Robots.txt for Better SEO
# This file controls how search engines crawl and index our site
# Admin panel and sensitive areas are completely blocked from all crawlers

# Block ALL bots from admin panel and related paths
User-agent: *
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password
Disallow: /_admin
Disallow: /api/admin
Disallow: /dashboard
Disallow: /management
Disallow: /private
Disallow: /temp
Disallow: /tmp

# Allow all other content for major search engines
User-agent: Googlebot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password

User-agent: Bingbot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*
Disallow: /login
Disallow: /reset-password

User-agent: Twitterbot
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*

User-agent: facebookexternalhit
Allow: /
Disallow: /admin
Disallow: /admin/
Disallow: /admin/*

# Block aggressive crawlers and scrapers that don't respect rate limits
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MegaIndex
Disallow: /

User-agent: BLEXBot
Disallow: /

# Crawl delay for respectful bots (helps with server load)
Crawl-delay: 1

# Sitemap location - critical for SEO
Sitemap: ${siteUrl}/sitemap.xml

# Allow important files for all crawlers
Allow: /favicon.ico
Allow: /favicon-*.png
Allow: /android-chrome-*.png
Allow: /apple-touch-icon.png
Allow: /site.webmanifest
Allow: /browserconfig.xml
Allow: /robots.txt
Allow: /sitemap.xml

# Block common exploit paths
Disallow: /wp-admin
Disallow: /wp-content
Disallow: /wp-includes
Disallow: /cgi-bin
Disallow: /.env
Disallow: /.git
Disallow: /node_modules
`;

  // Write to both public and dist directories
  [publicDir, distDir].forEach(dir => {
    if (fs.existsSync(dir)) {
      const robotsPath = path.join(dir, 'robots.txt');
      fs.writeFileSync(robotsPath, robotsContent);
      console.log(`✅ Enhanced robots.txt written to ${robotsPath}`);
    }
  });

  optimizations.robots = true;
}

/**
 * Create SEO-optimized meta tags template
 */
function generateMetaTagsTemplate() {
  console.log('📝 Generating SEO meta tags template...');
  
  const metaTemplate = `<!-- StreamDB SEO Meta Tags Template -->
<!-- Copy these meta tags to your HTML head section -->

<!-- Primary Meta Tags -->
<title>StreamDB - Free Movies & TV Series Online | Watch HD Content</title>
<meta name="title" content="StreamDB - Free Movies & TV Series Online | Watch HD Content">
<meta name="description" content="Watch free movies and TV series online in HD quality. StreamDB offers the latest movies, popular TV shows, and classic content. No registration required.">
<meta name="keywords" content="free movies, tv series, watch online, streaming, HD movies, latest movies, tv shows, entertainment">
<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
<meta name="language" content="English">
<meta name="author" content="StreamDB">
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="${siteUrl}/">
<meta property="og:title" content="StreamDB - Free Movies & TV Series Online">
<meta property="og:description" content="Watch free movies and TV series online in HD quality. Latest releases and classic content available.">
<meta property="og:image" content="${siteUrl}/og-image.jpg">
<meta property="og:site_name" content="StreamDB">
<meta property="og:locale" content="en_US">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="${siteUrl}/">
<meta property="twitter:title" content="StreamDB - Free Movies & TV Series Online">
<meta property="twitter:description" content="Watch free movies and TV series online in HD quality. Latest releases and classic content available.">
<meta property="twitter:image" content="${siteUrl}/twitter-image.jpg">

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="#000000">
<meta name="msapplication-TileColor" content="#000000">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- Canonical URL -->
<link rel="canonical" href="${siteUrl}/">

<!-- Preconnect to external domains for performance -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "StreamDB",
  "url": "${siteUrl}",
  "description": "Free online streaming platform for movies and TV series",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "${siteUrl}/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  },
  "publisher": {
    "@type": "Organization",
    "name": "StreamDB",
    "url": "${siteUrl}"
  }
}
</script>`;

  const metaPath = path.join(__dirname, '..', 'SEO_META_TAGS.html');
  fs.writeFileSync(metaPath, metaTemplate);
  console.log(`✅ SEO meta tags template created at ${metaPath}`);
  
  optimizations.metaTags = true;
}

/**
 * Submit sitemap to search engines
 */
async function submitSitemapToSearchEngines() {
  console.log('📡 Submitting sitemap to search engines...');
  
  const searchEngines = [
    {
      name: 'Google',
      url: `https://www.google.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
    },
    {
      name: 'Bing',
      url: `https://www.bing.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`
    }
  ];

  for (const engine of searchEngines) {
    try {
      await new Promise((resolve, reject) => {
        const req = https.get(engine.url, (res) => {
          if (res.statusCode === 200) {
            console.log(`✅ Successfully notified ${engine.name}`);
          } else {
            console.log(`⚠️  ${engine.name} returned status ${res.statusCode}`);
          }
          resolve();
        });
        
        req.on('error', (err) => {
          console.log(`❌ Failed to notify ${engine.name}: ${err.message}`);
          resolve(); // Don't reject, continue with other engines
        });
        
        req.setTimeout(10000, () => {
          req.destroy();
          console.log(`⏰ Timeout notifying ${engine.name}`);
          resolve();
        });
      });
    } catch (error) {
      console.log(`❌ Error notifying ${engine.name}: ${error.message}`);
    }
  }
}

/**
 * Generate comprehensive SEO report
 */
function generateSEOReport() {
  console.log('\n📊 SEO Optimization Report');
  console.log('=' .repeat(50));
  
  const completedTasks = Object.values(optimizations).filter(Boolean).length;
  const totalTasks = Object.keys(optimizations).length;
  
  console.log(`✅ Completed: ${completedTasks}/${totalTasks} optimizations`);
  console.log('\nOptimization Status:');
  
  Object.entries(optimizations).forEach(([task, completed]) => {
    const status = completed ? '✅' : '❌';
    const taskName = task.charAt(0).toUpperCase() + task.slice(1);
    console.log(`${status} ${taskName}`);
  });
  
  console.log('\n🎯 Next Steps for Better SEO:');
  console.log('1. Add the meta tags from SEO_META_TAGS.html to your main HTML template');
  console.log('2. Create high-quality content with proper headings (H1, H2, H3)');
  console.log('3. Optimize images with alt text and proper file names');
  console.log('4. Implement internal linking between related content');
  console.log('5. Monitor Google Search Console for indexing status');
  console.log('6. Submit your site to Google Search Console and Bing Webmaster Tools');
  console.log('7. Create quality backlinks from relevant websites');
  console.log('8. Ensure fast loading times (< 3 seconds)');
  console.log('9. Make sure your site is mobile-friendly');
  console.log('10. Regularly update content and sitemap');
}

/**
 * Main optimization function
 */
async function runSEOOptimization() {
  try {
    console.log('🔧 Starting SEO optimization process...\n');
    
    // Run optimizations
    optimizeRobotsTxt();
    generateMetaTagsTemplate();
    
    // Generate fresh sitemap
    console.log('🗺️  Generating fresh sitemap...');
    const { generateSitemap } = await import('./generate-sitemap.js');
    await generateSitemap();
    optimizations.sitemap = true;
    
    // Submit to search engines
    await submitSitemapToSearchEngines();
    
    // Generate report
    generateSEOReport();
    
    console.log('\n🎉 SEO optimization completed successfully!');
    console.log(`🌐 Your sitemap is available at: ${siteUrl}/sitemap.xml`);
    console.log(`🤖 Your robots.txt is available at: ${siteUrl}/robots.txt`);
    
  } catch (error) {
    console.error('❌ SEO optimization failed:', error.message);
    process.exit(1);
  }
}

// Run the optimizer
if (import.meta.url === `file://${process.argv[1]}`) {
  runSEOOptimization();
}

export { runSEOOptimization };
