# Nginx Reverse Proxy Configuration for streamdb.online
# SECURE SETUP - Only uses standard ports (80, 443)
# Deploy this to your reverse proxy server: /etc/nginx/sites-available/streamdb.online

server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name streamdb.online www.streamdb.online;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/streamdb.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/streamdb.online/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; connect-src 'self' https: wss: https://pertawee.net https://al5sm.com" always;
    
    # Real IP Configuration (for Cloudflare)
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;
    
    # ALL TRAFFIC TO BACKEND SERVER PORT 80
    # Since backend only has ports 80, 443, 22 open, we route everything through port 80
    location / {
        proxy_pass http://***********:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Preserve original request URI for backend routing
        proxy_set_header X-Original-URI $request_uri;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Logging
    access_log /var/log/nginx/streamdb_access.log;
    error_log /var/log/nginx/streamdb_error.log;
}
