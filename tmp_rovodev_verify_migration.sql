-- Run this AFTER the migration to verify everything worked correctly

-- Step 1: Check if new columns were added
DESCRIBE content;

-- Step 2: Check carousel items with new fields
SELECT 
    id, 
    title, 
    add_to_carousel, 
    carousel_position, 
    crop_settings,
    is_published,
    created_at 
FROM content 
WHERE add_to_carousel = 1 
ORDER BY carousel_position ASC;

-- Step 3: Count total carousel items
SELECT COUNT(*) as total_carousel_items 
FROM content 
WHERE add_to_carousel = 1;

-- Step 4: Check available content for carousel
SELECT 
    id, 
    title, 
    type,
    is_published,
    add_to_carousel
FROM content 
WHERE is_published = 1 AND add_to_carousel = 0
LIMIT 10;